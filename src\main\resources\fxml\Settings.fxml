<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.text.Font?>

<VBox alignment="TOP_CENTER" prefWidth="840.0" style="-fx-background-color: #f5f5f5;"
      xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.ceadeal.javafxboot.ctrl.SettingsCtrl"
      snapToPixel="true">
    <padding>
        <Insets top="20.0" right="20.0" bottom="20.0" left="20.0" />
    </padding>
    <children>
        <TabPane tabClosingPolicy="UNAVAILABLE" prefHeight="700.0" style="-fx-background-color: transparent;">
            <style>
                -fx-tab-min-width: 140px;
                -fx-tab-min-height: 45px;
                -fx-tab-max-height: 45px;
                -fx-background-color: #ffffff;
                -fx-focus-color: transparent;
                -fx-faint-focus-color: transparent;
                -fx-border-width: 0;
                -fx-font-smoothing-type: lcd;
            
                /* 禁用缩放和自适应 */
                * {
                    -fx-scale-x: 1.0;
                    -fx-scale-y: 1.0;
                    -fx-font-smooth-type: lcd;
                    -fx-text-fill: #303133 !important;
                    -fx-font-size: 14px;
                }

                /* 全局字体强制设置 */
                .label {
                    -fx-font-size: 14px !important;
                    -fx-text-fill: #303133 !important;
                }
                
                .text-field {
                    -fx-font-size: 14px !important;
                }
            
                /* Tab选中样式 */
                -fx-tab-background: #f5f5f5;
                -fx-background: #f5f5f5;
                
                .tab {
                    -fx-background-color: #f5f5f5;
                    -fx-border-color: transparent transparent #ddd transparent;
                    -fx-border-width: 3px 1px 1px 1px;
                    -fx-padding: 5px 15px;
                    -fx-cursor: hand;
                }
                
                .tab:selected {
                    -fx-background-color: #ffffff;
                    -fx-border-color: #409EFF #409EFF #ffffff #409EFF;
                    -fx-border-width: 3px 1px 0px 1px;
                    -fx-border-radius: 3px 3px 0 0;
                }
                
                .tab-label {
                    -fx-text-fill: #606266;
                }
                
                .tab:selected .tab-label {
                    -fx-text-fill: #409EFF;
                }
                
                .tab-header-background {
                    -fx-background-color: #f5f5f5;
                    -fx-border-color: transparent transparent #ddd transparent;
                    -fx-border-width: 0 0 1px 0;
                }
                
                .text-field {
                    -fx-background-insets: 0;
                    -fx-background-radius: 3;
                    -fx-border-width: 1px;
                    -fx-border-color: #dcdfe6;
                    -fx-border-radius: 3;
                    -fx-padding: 5px;
                    -fx-pref-height: 32px;
                    -fx-min-height: 32px;
                    -fx-max-height: 32px;
                    -fx-focus-color: transparent;
                    -fx-faint-focus-color: transparent;
                }
                
                .text-field:focused {
                    -fx-background-insets: 0;
                    -fx-background-radius: 3;
                    -fx-border-width: 1px;
                    -fx-border-color: #409EFF;
                    -fx-border-radius: 3;
                    -fx-padding: 5px;
                    -fx-pref-height: 32px;
                    -fx-min-height: 32px;
                    -fx-max-height: 32px;
                }
                
                .button {
                    -fx-background-radius: 3;
                    -fx-pref-height: 32px;
                    -fx-min-height: 32px;
                    -fx-max-height: 32px;
                    -fx-font-weight: bold;
                    -fx-cursor: hand;
                }
            </style>
            <tabs>
                <!-- 基本设置标签页 -->
                <Tab text="基本设置" style="-fx-font-size: 16px; -fx-font-weight: bold;">
                    <content>
                        <VBox spacing="12.0" style="-fx-background-color: #f5f5f5;">
                            <padding>
                                <Insets top="15.0" right="20.0" bottom="15.0" left="20.0" />
                            </padding>
                            <!-- 路径设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Cursor应用数据库路径,非安装路径" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="路径初始会自动读取，如果切换代理提示应用路径错误，则根据官网中的方法查找正确的目录"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <TextField fx:id="vscdbPathField" prefWidth="500.0" styleClass="text-field" focusTraversable="false" />
                                            <Button fx:id="savePathButton" text="保存路径" onAction="#onSavePathClicked"
                                                styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- Cursor安装路径设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Cursor安装路径设置" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="用于启用Claude Max及自动启动Cursor。对应Cursor.exe所在目录。"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <TextField fx:id="cursorPathField" prefWidth="500.0" styleClass="text-field" focusTraversable="false" promptText="请输入Cursor安装路径" />
                                            <Button fx:id="saveCursorPathButton" text="保存路径" onAction="#onSaveCursorPathClicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- 设备绑定设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="设备绑定" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="切换代理功能只有绑定的设备能使用，账号首次登录自动绑定，每日绑定设备次数有限制" style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <Button fx:id="bindDeviceButton" text="绑定设备" onAction="#onBindDeviceClicked" 
                                                   styleClass="button" style="-fx-background-color: #67C23A; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>
                            
                            <!-- Token设置 -->
                            <VBox visible="false" alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Cursor Token更新" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="输入Token后，将使用此Token自动更新当前Cursor授权信息" style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <TextField fx:id="tokenField" prefWidth="500.0" styleClass="text-field" focusTraversable="false" promptText="请输入Token" />
                                            <Button fx:id="saveTokenButton" text="保存Token" onAction="#onSaveTokenClicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>
                        </VBox>
                    </content>
                </Tab>
                
                <!-- 功能设置标签页 -->
                <Tab text="功能设置" style="-fx-font-size: 16px; -fx-font-weight: bold;">
                    <content>
                        <VBox spacing="12.0" style="-fx-background-color: #f5f5f5;">
                            <padding>
                                <Insets top="15.0" right="20.0" bottom="15.0" left="20.0" />
                            </padding>
                            <!-- Claude 3.7 Max支持设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Max支持" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="开启支持Max，使用o3模型，防检测等效果。默认自动开启"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <Button  fx:id="enableClaudeMaxButton" text="启用Max" onAction="#onEnableClaudeMaxClicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                            <Button  fx:id="restoreClaudeButton" text="还原设置" onAction="#onRestoreClaudeClicked"
                                                   styleClass="button" style="-fx-background-color: #F56C6C; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- HTTP2设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="HTTP/2 设置" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="cursor提示该异常时可尝试禁用/启用，然后新建聊天窗口：please check your internet connection or VPN"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <Button fx:id="disableHttp2Button" text="禁用HTTP/2" onAction="#onDisableHttp2Clicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                            <Button fx:id="enableHttp2Button" text="启用HTTP/2" onAction="#onEnableHttp2Clicked"
                                                   styleClass="button" style="-fx-background-color: #67C23A; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- Cursor代理设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Cursor代理设置" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="配置正确但还是区域限制的可点击 禁用/启用http2按钮，记得设置后续重启cursor新聊天会话"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <TextField fx:id="proxyField" prefWidth="300.0" styleClass="text-field" focusTraversable="false" promptText="socks5://127.0.0.1:7890" />
                                            <Button fx:id="fillDefaultProxyButton" text="默认填充" onAction="#onFillDefaultProxyClicked"
                                                   styleClass="button" style="-fx-background-color: #67C23A; -fx-text-fill: white;" />
                                            <Button fx:id="saveProxyButton" text="保存设置" onAction="#onSaveProxyClicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- 关闭自动更新设置 -->
                            <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);"
                                  maxWidth="700.0" minHeight="100.0">
                                <padding>
                                    <Insets top="20.0" right="25.0" bottom="20.0" left="25.0" />
                                </padding>
                                <children>
                                    <Label text="Cursor自动更新设置" style="-fx-font-weight: bold; -fx-text-fill: #303133;" wrapText="true">
                                        <font>
                                            <Font size="15.0" />
                                        </font>
                                    </Label>
                                    <Label text="控制Cursor是否自动更新"
                                           style="-fx-text-fill: #606266;" wrapText="true">
                                        <font>
                                            <Font size="13.0" />
                                        </font>
                                    </Label>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" minHeight="32.0" prefHeight="32.0" maxHeight="32.0" VBox.vgrow="NEVER">
                                        <children>
                                            <Button fx:id="disableAutoUpdateButton" text="禁用自动更新" onAction="#onDisableAutoUpdateClicked"
                                                   styleClass="button" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
                                            <Button fx:id="enableAutoUpdateButton" text="启用自动更新" onAction="#onEnableAutoUpdateClicked"
                                                   styleClass="button" style="-fx-background-color: #67C23A; -fx-text-fill: white;" />
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>

                            <!-- 这里可以为未来添加更多功能设置预留位置 -->
                            <VBox alignment="TOP_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" 
                                  maxWidth="700.0" minHeight="100.0" visible="false">
                                <padding>
                                    <Insets top="30.0" right="30.0" bottom="30.0" left="30.0" />
                                </padding>
                                <children>
                                    <Label text="未来功能设置" style="-fx-font-weight: bold; -fx-text-fill: #303133;">
                                        <font>
                                            <Font size="16.0" />
                                        </font>
                                    </Label>
                                </children>
                            </VBox>
                        </VBox>
                    </content>
                </Tab>
            </tabs>
        </TabPane>
    </children>
</VBox>