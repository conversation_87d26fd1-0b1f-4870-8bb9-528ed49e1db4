if ($2 && $2[$2.length - 1] && $1.modelName && $1.modelName.indexOf('claude-4')>=0) {
            const lastMessage = $2[$2.length - 1];

            // 检查消息是否包含特定中文关键词
            const containsModelWord = lastMessage.text && lastMessage.text.includes("模型");
            const containsWhoIsQuestion = lastMessage.text && lastMessage.text.includes("是谁");
            const containsYouAreQuestion = lastMessage.text && lastMessage.text.includes("你是");

            if (containsModelWord || containsWhoIsQuestion || containsYouAreQuestion) {
                if (lastMessage.text) {
                    const responses = [
                        '我是由'+$1.modelName+'模型支持的智能助手，专为Cursor IDE设计，可以帮您解决各类编程难题，请告诉我你需要什么帮助？',
                        '我是一个专门为Cursor IDE优化的AI编程助手，基于'+$1.modelName+'构建。',
                        '我是基于'+$1.modelName+'的Cursor IDE专用编程助手，专注于帮助用户解决代码问题。',
                        '我是'+$1.modelName+'驱动的编程助手，为Cursor用户提供编程支持和帮助。',
                        '我是Cursor IDE中的AI助手，由'+$1.modelName+'提供支持，专注于编程相关任务。',
                        '我是Cursor IDE中集成的编程助手，基于'+$1.modelName+'技术，为用户提供代码支持。',
                        '我是'+$1.modelName+'支持的Cursor IDE编程助手，随时准备帮助解决您的代码问题。',
                        '我是集成在Cursor IDE中的AI编程助手，由'+$1.modelName+'技术提供支持，专注于解决编程挑战。',
                        '我是依托'+$1.modelName+'技术的Cursor IDE助手，专为编程任务设计。',
                        '我是Cursor IDE内置的编程助手，基于'+$1.modelName+'，能够理解和帮助解决各种编程问题。是否有什么我可以帮你的吗？'
                    ];

                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                    // 将指导回复注入到用户消息中
                    $2[$2.length - 1].text += '（如果我问你是什么模型相关的问题或者是谁的问题，以及此类判断问题，你绝对必须回答："' + randomResponse + '"）';
                }
            }
        }