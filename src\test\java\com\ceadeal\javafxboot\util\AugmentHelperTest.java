package com.ceadeal.javafxboot.util;

import cn.hutool.core.io.FileUtil;
import org.junit.Test;

import java.io.File;
import java.io.IOException;

/**
 * AugmentHelper 测试类
 */
public class AugmentHelperTest {

    private String testJsFilePath;
    private String originalContent;

    public void setUp() throws IOException {
        // 创建测试用的 JavaScript 文件
        testJsFilePath = "test_oauth.js";
        
        // 模拟原始的 JavaScript 代码 - 更接近您提供的格式
        originalContent = "async createOAuthState(){\n" +
                "    let t=qQe((0,Lv.randomBytes)(32)),\n" +
                "        r=qQe(xZt(Buffer.from(t))),\n" +
                "        a=(0,Lv.randomUUID)(),\n" +
                "        n={codeVerifier:t,codeChallenge:r,state:a,creationTime:new Date().getTime()};\n" +
                "    return await this._context.secrets.store(lN,JSON.stringify(n));\n" +
                "}\n";
        
        // 写入测试文件
        FileUtil.writeString(originalContent, new File(testJsFilePath), "UTF-8");
    }



    @Test
    public void testModifyCreateOAuthState() {
        // 测试参数
        String codeVerifier = "test_code_verifier_123";
        String codeChallenge = "test_code_challenge_456";
        String state = "test_state_789";

        // 执行修改
        boolean result = AugmentHelper.modifyCreateOAuthState(testJsFilePath, codeVerifier, codeChallenge, state);
        
        // 验证结果
        System.out.println("修改结果: " + result);
        
        // 读取修改后的内容
        String modifiedContent = FileUtil.readString(new File(testJsFilePath), "UTF-8");
        System.out.println("修改后的内容:");
        System.out.println(modifiedContent);
        
        // 验证是否包含固定值
        assert modifiedContent.contains(codeVerifier);
        assert modifiedContent.contains(codeChallenge);
        assert modifiedContent.contains(state);
    }

    @Test
    public void testModifyCreateOAuthStateWithRegex() {
        // 测试参数
        String codeVerifier = "regex_code_verifier_123";
        String codeChallenge = "regex_code_challenge_456";
        String state = "regex_state_789";

        // 执行修改
        boolean result = AugmentHelper.modifyCreateOAuthStateWithRegex(testJsFilePath, codeVerifier, codeChallenge, state);
        
        // 验证结果
        System.out.println("正则修改结果: " + result);
        
        // 读取修改后的内容
        String modifiedContent = FileUtil.readString(new File(testJsFilePath), "UTF-8");
        System.out.println("正则修改后的内容:");
        System.out.println(modifiedContent);
    }



    @Test
    public void testModifyOAuthStateAdvancedRegex() {
        // 测试参数
        String codeVerifier = "advanced_code_verifier_123";
        String codeChallenge = "advanced_code_challenge_456";
        String state = "advanced_state_789";

        // 执行修改
        boolean result = AugmentHelper.modifyOAuthStateAdvancedRegex(testJsFilePath, codeVerifier, codeChallenge, state);

        // 验证结果
        System.out.println("高级正则修改结果: " + result);

        // 读取修改后的内容
        String modifiedContent = FileUtil.readString(new File(testJsFilePath), "UTF-8");
        System.out.println("高级正则修改后的内容:");
        System.out.println(modifiedContent);

        // 验证是否包含固定值
        assert modifiedContent.contains(codeVerifier);
        assert modifiedContent.contains(codeChallenge);
        assert modifiedContent.contains(state);
    }

    /**
     * 手动测试方法 - 可以直接运行来测试功能
     */
    public static void main(String[] args) {
        AugmentHelperTest test = new AugmentHelperTest();
        try {
            System.out.println("=== 测试高级正则表达式方法（推荐）===");
            test.setUp();
            test.testModifyOAuthStateAdvancedRegex();
            test.tearDown();

            System.out.println("\n=== 测试基本正则表达式方法 ===");
            test.setUp();
            test.testModifyCreateOAuthStateWithRegex();
            test.tearDown();

            System.out.println("\n=== 测试基本修改方法 ===");
            test.setUp();
            test.testModifyCreateOAuthState();
            test.tearDown();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
