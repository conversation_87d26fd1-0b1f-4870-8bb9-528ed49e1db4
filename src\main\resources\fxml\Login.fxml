<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="400.0" prefWidth="600.0" style="-fx-background-color: #f5f5f5;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.LoginCtrl">
   <children>
      <VBox alignment="CENTER" layoutX="150.0" layoutY="50.0" prefHeight="300.0" prefWidth="340.0" spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" AnchorPane.bottomAnchor="50.0" AnchorPane.leftAnchor="130.0" AnchorPane.rightAnchor="130.0" AnchorPane.topAnchor="50.0">
         <padding>
            <Insets bottom="30.0" left="20.0" right="20.0" top="30.0" />
         </padding>
         <children>
            <Label text="系统登录" textAlignment="CENTER" style="-fx-text-fill: #303133;">
               <font>
                  <Font size="24.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="20.0" />
               </VBox.margin>
            </Label>
            <TextField fx:id="usernameField" promptText="用户名" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets bottom="10.0" />
               </VBox.margin>
            </TextField>
            <PasswordField fx:id="passwordField" promptText="密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets bottom="10.0" />
               </VBox.margin>
            </PasswordField>
            <HBox alignment="CENTER_LEFT" maxWidth="300.0">
               <children>
                  <CheckBox fx:id="rememberPasswordCheckBox" mnemonicParsing="false" text="记住密码" style="-fx-text-fill: #606266;">
                     <HBox.margin>
                        <Insets left="85.0" />
                     </HBox.margin>
                  </CheckBox>
               </children>
               <VBox.margin>
                  <Insets bottom="15.0" />
               </VBox.margin>
            </HBox>
            <HBox alignment="CENTER" spacing="20.0">
               <children>
                  <Button fx:id="loginButton" defaultButton="true" mnemonicParsing="false" onAction="#onLoginButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="登 录">
                     <HBox.margin>
                        <Insets />
                     </HBox.margin>
                  </Button>
                  <Button fx:id="registerButton" mnemonicParsing="false" onAction="#onRegisterButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #67C23A; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="注 册">
                     <HBox.margin>
                        <Insets />
                     </HBox.margin>
                  </Button>
               </children>
            </HBox>
            <Button fx:id="noticeButton" mnemonicParsing="false" onAction="#onNoticeButtonClicked" prefHeight="35.0" prefWidth="150.0" style="-fx-background-color: transparent; -fx-text-fill: #409EFF; -fx-border-color: transparent; -fx-cursor: hand; -fx-font-size: 14px; -fx-font-weight: bold;" text="公告及使用须知信息">
               <VBox.margin>
                  <Insets top="15.0" />
               </VBox.margin>
            </Button>
         </children>
      </VBox>
   </children>
</AnchorPane> 