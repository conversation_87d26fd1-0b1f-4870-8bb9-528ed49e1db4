package com.ceadeal.javafxboot.view;

import de.felixroske.jfxsupport.AbstractFxmlView;
import de.felixroske.jfxsupport.FXMLView;
import javafx.fxml.FXML;
import javafx.scene.Scene;
import javafx.stage.Stage;

@FXMLView(value = "/fxml/Main.fxml", css = "/css/Main.css", bundle = "")
public class MainView extends AbstractFxmlView {
    @FXML
    private void initialize() {
        // 获取当前的 Scene 对象
        Scene scene = getView().getScene(); // 获取当前视图的 Scene

        // 获取当前 Stage
        Stage stage = (Stage) scene.getWindow(); // 从 Scene 获取 Stage

        // 添加 BootstrapFX 样式表
//        scene.getStylesheets().add("org/kordamp/bootstrapfx/bootstrapfx.css");
    }
}
