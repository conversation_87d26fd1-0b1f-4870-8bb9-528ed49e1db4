package com.ceadeal.javafxboot.pojo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @version 1.0.0
 * <AUTHOR>
 * @Date 2024/7/17
 * @describe cursor请求记录
 */
@Data
public class CursorEmailRecordDO implements Serializable {
    /**
     * ID
     */
    private Long id;
    /**
     * 邮箱号
     */
    private String mail;
    /**
     * 是否注册成功
     */
    private String useSuccessFlag;
    /**
     * accessToken，WorkosCursorSessionToken的后半部分
     */
    private String token;
    /**
     * WorkosCursorSessionToken的前部分
     */
    private String cursorSeesionId;
    private String accessToken;
    private String refreshToken;
    /**
     * 使用用户
     */
    private Long userId;
    /**
     * 使用用户名
     */
    private String username;
    private LocalDateTime createTime;
}
