package com.ceadeal.javafxboot.ctrl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.pojo.CursorEmailRecordDO;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.util.CursorHelper;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 邮箱记录对话框控制器
 */
@Slf4j
public class EmailRecordDialogCtrl {
    
    private final ApiService apiService;
    private final Stage dialogStage;
    
    private TableView<EmailRecordItem> tableView;
    private ObservableList<EmailRecordItem> tableData;
    private Pagination pagination;
    private Label totalLabel;
    private ProgressIndicator loadingIndicator;
    private VBox contentContainer;
    
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalRecords = 0;
    
    public EmailRecordDialogCtrl(ApiService apiService, Stage dialogStage) {
        this.apiService = apiService;
        this.dialogStage = dialogStage;
        this.tableData = FXCollections.observableArrayList();
    }
    
    /**
     * 创建对话框内容
     */
    public VBox createContent() {
        VBox root = new VBox(20);
        root.setPadding(new Insets(25));
        root.setStyle("-fx-background-color: linear-gradient(to bottom, #f8f9fa, #ffffff);");

        // 标题区域
        HBox titleBox = new HBox(10);
        titleBox.setAlignment(Pos.CENTER_LEFT);
        titleBox.setPadding(new Insets(0, 0, 15, 0));
        titleBox.setStyle("-fx-border-color: #e9ecef; -fx-border-width: 0 0 2 0;");

        Label titleLabel = new Label("📧 邮箱使用记录(近3天)");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        titleBox.getChildren().add(titleLabel);

        // 创建表格容器
        VBox tableContainer = new VBox(10);
        tableContainer.setStyle("-fx-background-color: white; -fx-background-radius: 8; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 8, 0, 0, 2); -fx-padding: 15;");

        // 创建表格
        createTable();

        // 创建分页控件
        createPagination();

        // 创建加载指示器
        loadingIndicator = new ProgressIndicator();
        loadingIndicator.setPrefSize(60, 60);
        loadingIndicator.setVisible(false);
        loadingIndicator.setStyle("-fx-progress-color: #409EFF;");

        // 总记录数标签
        totalLabel = new Label("总记录数: 0");
        totalLabel.setStyle("-fx-font-size: 13px; -fx-text-fill: #6c757d; -fx-font-weight: bold;");

        // 内容容器
        contentContainer = new VBox(15);
        contentContainer.getChildren().addAll(tableView, pagination);

        tableContainer.getChildren().addAll(contentContainer);

        // 底部信息栏
        HBox bottomBox = new HBox(15);
        bottomBox.setAlignment(Pos.CENTER_LEFT);
        bottomBox.setPadding(new Insets(10, 0, 0, 0));
        bottomBox.getChildren().addAll(totalLabel);

        root.getChildren().addAll(titleBox, tableContainer, bottomBox);

        // 初始加载数据
        loadData(1);

        return root;
    }
    
    /**
     * 创建表格
     */
    private void createTable() {
        tableView = new TableView<>();
        tableView.setItems(tableData);
        tableView.setPrefHeight(420);
        tableView.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-radius: 6; -fx-border-width: 1;");

        // 设置行高和悬停效果
        tableView.setRowFactory(tv -> {
            TableRow<EmailRecordItem> row = new TableRow<>();
            row.setPrefHeight(50); // 增加行高到50
            row.setStyle("-fx-background-color: white; -fx-border-color: #f8f9fa; -fx-border-width: 0 0 1 0;");
            row.setOnMouseEntered(e -> {
                if (!row.isEmpty()) {
                    row.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #e9ecef; -fx-border-width: 0 0 1 0;");
                }
            });
            row.setOnMouseExited(e -> {
                if (!row.isEmpty()) {
                    row.setStyle("-fx-background-color: white; -fx-border-color: #f8f9fa; -fx-border-width: 0 0 1 0;");
                }
            });
            return row;
        });

        // 禁用列重新排序
        tableView.getColumns().addListener((javafx.collections.ListChangeListener<TableColumn<EmailRecordItem, ?>>) change -> {
            change.next();
            if (change.wasReplaced()) {
                tableView.getColumns().clear();
                createTableColumns();
            }
        });
        
        createTableColumns();
    }

    /**
     * 创建表格列
     */
    private void createTableColumns() {
        tableView.getColumns().clear();

        // 邮箱列 - 增加宽度，添加样式
        TableColumn<EmailRecordItem, String> emailCol = new TableColumn<>("邮箱地址");
        emailCol.setCellValueFactory(new PropertyValueFactory<>("email"));
        emailCol.setPrefWidth(280);
        emailCol.setResizable(true);
        emailCol.setCellFactory(column -> new TableCell<EmailRecordItem, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    setStyle("-fx-font-family: 'Consolas', 'Monaco', monospace; -fx-font-size: 12px; -fx-text-fill: #333333; -fx-padding: 8 12;");
                }
            }
        });

        // 使用时间列
        TableColumn<EmailRecordItem, String> timeCol = new TableColumn<>("使用时间");
        timeCol.setCellValueFactory(new PropertyValueFactory<>("createTime"));
        timeCol.setPrefWidth(140);
        timeCol.setResizable(true);
        timeCol.setCellFactory(column -> new TableCell<EmailRecordItem, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    setStyle("-fx-font-size: 12px; -fx-text-fill: #666666; -fx-padding: 8 12;");
                }
            }
        });

        // 账号级别列
        TableColumn<EmailRecordItem, String> levelCol = new TableColumn<>("账号级别");
        levelCol.setCellValueFactory(new PropertyValueFactory<>("membershipType"));
        levelCol.setPrefWidth(100);
        levelCol.setResizable(true);
        levelCol.setCellFactory(column -> new TableCell<EmailRecordItem, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    if ("Pro Trial".equals(item)) {
                        setStyle("-fx-text-fill: #409EFF; -fx-font-weight: bold; -fx-font-size: 12px; -fx-padding: 8 12;");
                    } else if ("free".equals(item)) {
                        setStyle("-fx-text-fill: #909399; -fx-font-weight: bold; -fx-font-size: 12px; -fx-padding: 8 12;");
                    } else {
                        setStyle("-fx-text-fill: #333333; -fx-font-weight: bold; -fx-font-size: 12px; -fx-padding: 8 12;");
                    }
                }
            }
        });

        // 状态列
        TableColumn<EmailRecordItem, String> statusCol = new TableColumn<>("状态");
        statusCol.setCellValueFactory(new PropertyValueFactory<>("status"));
        statusCol.setPrefWidth(80);
        statusCol.setResizable(true);
        statusCol.setCellFactory(column -> new TableCell<EmailRecordItem, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    if ("有效".equals(item)) {
                        setStyle("-fx-text-fill: #67C23A; -fx-font-weight: bold; -fx-font-size: 12px; -fx-background-color: #f0f9ff; -fx-background-radius: 4; -fx-padding: 8 12;");
                    } else if ("过期".equals(item)) {
                        setStyle("-fx-text-fill: #F56C6C; -fx-font-weight: bold; -fx-font-size: 12px; -fx-background-color: #fef0f0; -fx-background-radius: 4; -fx-padding: 8 12;");
                    } else {
                        setStyle("-fx-text-fill: #909399; -fx-font-size: 12px; -fx-padding: 8 12;");
                    }
                }
            }
        });

        // 操作列
        TableColumn<EmailRecordItem, Void> actionCol = new TableColumn<>("操作");
        actionCol.setPrefWidth(100);
        actionCol.setResizable(false);
        actionCol.setSortable(false);
        actionCol.setCellFactory(column -> new TableCell<EmailRecordItem, Void>() {
            private final Button switchBtn = new Button("切换");

            {
                switchBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 6; -fx-cursor: hand; -fx-padding: 6 16;");
                switchBtn.setPrefWidth(70);
                switchBtn.setOnAction(event -> {
                    EmailRecordItem item = getTableView().getItems().get(getIndex());
                    switchToEmail(item);
                });

                // 添加悬停效果
                switchBtn.setOnMouseEntered(e ->
                    switchBtn.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 6; -fx-cursor: hand; -fx-padding: 6 16; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 4, 0, 0, 2);"));
                switchBtn.setOnMouseExited(e ->
                    switchBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 6; -fx-cursor: hand; -fx-padding: 6 16;"));
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(switchBtn);
                    setAlignment(Pos.CENTER);
                }
            }
        });

        tableView.getColumns().addAll(emailCol, timeCol, levelCol, statusCol, actionCol);

        // 设置表格列宽度策略，避免出现空白列
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

        // 禁用列重新排序
        tableView.getColumns().addListener((javafx.collections.ListChangeListener<TableColumn<EmailRecordItem, ?>>) change -> {
            change.next();
            if (change.wasReplaced()) {
                // 如果列被重新排序，恢复原始顺序
                tableView.getColumns().clear();
                tableView.getColumns().addAll(emailCol, timeCol, levelCol, statusCol, actionCol);
            }
        });

        // 设置表格头部样式
        Platform.runLater(() -> {
            try {
                if (tableView.lookup(".column-header-background") != null) {
                    tableView.lookup(".column-header-background").setStyle("-fx-background-color: #f8f9fa;");
                }
                if (tableView.lookup(".column-header") != null) {
                    tableView.lookup(".column-header").setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 0 1 1 0; -fx-font-weight: bold; -fx-text-fill: #495057;");
                }
            } catch (Exception e) {
                log.debug("设置表格头部样式失败", e);
            }
        });
    }

    /**
     * 创建分页控件
     */
    private void createPagination() {
        pagination = new Pagination();
        pagination.setPageCount(1);
        pagination.setCurrentPageIndex(0);
        pagination.setMaxPageIndicatorCount(7);
        pagination.setPageFactory(this::createPage);
        pagination.setStyle("-fx-background-color: transparent; -fx-border-color: #e9ecef; -fx-border-width: 1 0 0 0; -fx-padding: 15 0 0 0;");
    }
    
    /**
     * 分页工厂方法
     */
    private VBox createPage(int pageIndex) {
        currentPage = pageIndex + 1;
        loadData(currentPage);
        return new VBox(); // 返回空容器，实际内容在tableView中
    }
    
    /**
     * 加载数据
     */
    private void loadData(int page) {
        // 显示加载指示器
        showLoading(true);
        
        ThreadUtil.execAsync(() -> {
            try {
                // 调用API获取数据
                JSONObject params = new JSONObject();
                params.put("pageNo", page);
                params.put("pageSize", pageSize);
                
                CommonResult<JSONObject> result = apiService.getCursorEmailRecord(params);
                
                if (result.getIsSuccess() && result.getData() != null) {
                    JSONObject data = result.getData();
                    List<CursorEmailRecordDO> list = JSONUtil.toList(data.getJSONArray("list"), CursorEmailRecordDO.class);
                    totalRecords = data.getInt("total", 0);
                    
                    // 并行处理每个邮箱记录，获取账号级别和状态
                    List<CompletableFuture<EmailRecordItem>> futures = list.parallelStream()
                            .map(record -> CompletableFuture.supplyAsync(() -> {
                                EmailRecordItem item = new EmailRecordItem();
                                item.setId(record.getId());
                                item.setEmail(record.getMail().replace("2925.com", "outlook.com"));
                                item.setAccessToken(record.getAccessToken());
                                item.setRefreshToken(record.getRefreshToken());
                                item.setToken(record.getToken());
                                
                                // 格式化时间
                                if (record.getCreateTime() != null) {
                                    item.setCreateTime(record.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                                } else {
                                    item.setCreateTime("");
                                }
                                
                                // 获取账号级别和状态
                                try {
                                    JSONObject emailInfo = getEmailInfo(record.getAccessToken());
                                    if (emailInfo != null) {
                                        String membershipType = emailInfo.getStr("membershipType");
                                        String daysRemainingOnTrial = emailInfo.getStr("daysRemainingOnTrial");
                                        
                                        // 格式化账号级别显示
                                        String displayMembershipType = "free_trial".equals(membershipType) ? "Pro Trial" : membershipType;
                                        item.setMembershipType(displayMembershipType);
                                        
                                        // 设置状态
                                        if (NumberUtil.isNumber(daysRemainingOnTrial)) {
                                            int days = Integer.parseInt(daysRemainingOnTrial);
                                            item.setStatus(days > 0 ? "有效" : "过期");
                                        } else {
                                            item.setStatus("未知");
                                        }
                                    } else {
                                        item.setMembershipType("未知");
                                        item.setStatus("未知");
                                    }
                                } catch (Exception e) {
                                    log.error("获取邮箱{}的账号信息失败", record.getMail(), e);
                                    item.setMembershipType("获取失败");
                                    item.setStatus("未知");
                                }
                                
                                return item;
                            }))
                            .collect(Collectors.toList());
                    
                    // 等待所有异步任务完成
                    List<EmailRecordItem> items = futures.stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.toList());
                    
                    // 更新UI
                    Platform.runLater(() -> {
                        tableData.clear();
                        tableData.addAll(items);
                        
                        // 更新分页
                        int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                        pagination.setPageCount(Math.max(1, totalPages));
                        
                        // 更新总记录数
                        totalLabel.setText("总记录数: " + totalRecords);
                        
                        showLoading(false);
                    });
                } else {
                    Platform.runLater(() -> {
                        showAlert("错误", "获取邮箱记录失败：" + (result != null ? result.getMsg() : "未知错误"));
                        showLoading(false);
                    });
                }
            } catch (Exception e) {
                log.error("加载邮箱记录数据失败", e);
                Platform.runLater(() -> {
                    showAlert("错误", "加载数据失败：" + e.getMessage());
                    showLoading(false);
                });
            }
        });
    }
    
    /**
     * 显示/隐藏加载指示器
     */
    private void showLoading(boolean show) {
        Platform.runLater(() -> {
            if (show) {
                if (!contentContainer.getChildren().contains(loadingIndicator)) {
                    contentContainer.getChildren().add(0, loadingIndicator);
                }
                loadingIndicator.setVisible(true);
                tableView.setDisable(true);
                pagination.setDisable(true);
            } else {
                loadingIndicator.setVisible(false);
                contentContainer.getChildren().remove(loadingIndicator);
                tableView.setDisable(false);
                pagination.setDisable(false);
            }
        });
    }

    /**
     * 切换到指定邮箱
     */
    private void switchToEmail(EmailRecordItem item) {
        // 首先进行设备码校验
        ThreadUtil.execAsync(() -> {
            try {
                // 获取当前设备码
                String currentDeviceCode = SettingsCtrl.getCPUSerial();
                if (currentDeviceCode == null || currentDeviceCode.isEmpty()) {
                    Platform.runLater(() -> showAlert("错误", "获取当前设备码失败，无法进行校验"));
                    return;
                }

                // 获取用户信息中的绑定设备码
                CommonResult<JSONObject> userInfoResult = apiService.getUserInfo();
                if (!userInfoResult.getIsSuccess()) {
                    Platform.runLater(() -> showAlert("错误", "获取用户信息失败：" + userInfoResult.getMsg()));
                    return;
                }

                String boundDeviceCode = userInfoResult.getData().getStr("deviceCode");

                // 校验设备码是否一致
                if (boundDeviceCode == null || !boundDeviceCode.equals(currentDeviceCode)) {
                    Platform.runLater(() -> {
                        Alert deviceAlert = new Alert(Alert.AlertType.WARNING);
                        deviceAlert.setTitle("设备校验失败");
                        deviceAlert.setHeaderText("设备码不匹配");
                        deviceAlert.setContentText("当前设备码与绑定的设备码不一致，请先到菜单 系统设备->绑定设备 进行设备绑定。");
                        deviceAlert.showAndWait();
                    });
                    return;
                }

                // 设备码校验通过，继续切换流程
                Platform.runLater(() -> {
                    // 确认对话框
                    Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
                    confirmAlert.setTitle("确认切换");
                    confirmAlert.setHeaderText("切换代理邮箱");
                    confirmAlert.setContentText("确定要切换到邮箱 " + item.getEmail() + " 吗？\n这将重置机器码并重启Cursor应用。");

                    confirmAlert.showAndWait().ifPresent(response -> {
                        if (response == ButtonType.OK) {
                            performEmailSwitch(item);
                        }
                    });
                });

            } catch (Exception e) {
                log.error("设备码校验失败", e);
                Platform.runLater(() -> showAlert("错误", "设备码校验失败：" + e.getMessage()));
            }
        });
    }

    /**
     * 执行邮箱切换操作
     */
    private void performEmailSwitch(EmailRecordItem item) {
        ThreadUtil.execAsync(() -> {
            try {
                Platform.runLater(() -> showAlert("提示", "正在切换代理邮箱，请稍候..."));

                // 关闭cursor应用
                try {
                    CursorHelper.executeWindowsCommand("taskkill /f /im Cursor.exe");
                    log.info("关闭Cursor应用成功");
                } catch (Exception e) {
                    log.error("关闭Cursor应用失败", e);
                }

                // 更新认证信息
                String email = item.getEmail();
                String accessToken = item.getAccessToken();
                String refreshToken = item.getRefreshToken();

                boolean updateResult = CursorHelper.updateAuthInfo(email, accessToken, refreshToken,
                        apiService.getAppSettings().getVscdbPath());

                if (!updateResult) {
                    Platform.runLater(() -> showAlert("错误", "更新认证信息失败，请检查Cursor数据路径配置"));
                    return;
                }

                // 重置机器码
                boolean resetResult = CursorHelper.resetMachineCode(
                        apiService.getAppSettings().getVscdbPath(),
                        apiService.getAppSettings().getCursorPath());

                if (!resetResult) {
                    Platform.runLater(() -> showAlert("警告", "邮箱切换成功，但机器码重置失败，请手动重置"));
                }

                // 启动Cursor
                SettingsCtrl.openCursor(apiService.getAppSettings().getCursorPath());

                Platform.runLater(() -> {
                    showAlert("成功", "代理邮箱切换成功！");
                    dialogStage.close(); // 关闭对话框
                });

            } catch (Exception e) {
                log.error("切换代理邮箱失败", e);
                Platform.runLater(() -> showAlert("错误", "切换失败：" + e.getMessage()));
            }
        });
    }

    /**
     * 获取邮箱信息
     */
    private static JSONObject getEmailInfo(String accessToken) {
        try {
            HttpRequest doget = HttpUtil.createGet("https://api2.cursor.sh/auth/full_stripe_profile");
            doget.header("Authorization", "Bearer " + accessToken);
            doget.timeout(5000); // 5秒超时
            HttpResponse execute = doget.execute();
            String body = execute.body();

            if (StrUtil.isNotBlank(body)) {
                return JSONUtil.parseObj(body);
            }
        } catch (Exception e) {
            log.error("获取邮箱信息失败，accessToken: {}", accessToken, e);
        }
        return null;
    }

    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }

    /**
     * 邮箱记录项数据类
     */
    public static class EmailRecordItem {
        private Long id;
        private String email;
        private String createTime;
        private String membershipType;
        private String status;
        private String accessToken;
        private String refreshToken;
        private String token;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getMembershipType() { return membershipType; }
        public void setMembershipType(String membershipType) { this.membershipType = membershipType; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getAccessToken() { return accessToken; }
        public void setAccessToken(String accessToken) { this.accessToken = accessToken; }

        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }

        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
    }
}
