package com.ceadeal.javafxboot.ctrl;

import cn.hutool.core.util.StrUtil;
import com.ceadeal.javafxboot.Application;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.util.AppSettings;
import com.ceadeal.javafxboot.view.MainView;
import com.ceadeal.javafxboot.view.RegisterView;
import com.ceadeal.javafxboot.view.NoticeView;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 登录页面控制器
 */
@Slf4j
@FXMLController
public class LoginCtrl implements Initializable {

    @FXML
    private TextField usernameField;

    @FXML
    private PasswordField passwordField;

    @FXML
    private CheckBox rememberPasswordCheckBox;

    @FXML
    private Button loginButton;
    
    @FXML
    private Button registerButton;

    @FXML
    private Button noticeButton;

    @Autowired
    private ApiService apiService;

    @Autowired
    private AppSettings appSettings;

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Login view initialized");
        // 确保登录按钮可用
        loginButton.setDisable(false);
        // 加载用户配置
        appSettings.loadConfig();
        
        // 如果记住了密码，则填充用户名和密码
        if (appSettings.isRemember() && appSettings.getUsername() != null) {
            usernameField.setText(appSettings.getUsername());
            passwordField.setText(appSettings.getPassword());
            rememberPasswordCheckBox.setSelected(true);
            
            // 检查是否有有效的token，有则自动登录
            if (appSettings.hasValidToken()) {
                log.info("检测到有效token，尝试自动登录");
                // 在UI线程外执行自动登录，避免阻塞UI
                new Thread(() -> {
                    // 验证token是否有效
                    String validationMsg = apiService.validateToken(appSettings.getToken());
                    if (StrUtil.isBlank(validationMsg)) {
                        // token有效，直接跳转到主页
                        Platform.runLater(() -> {
                            try {
                                // 先获取MainCtrl实例
                                MainCtrl mainCtrl = Application.getApplicationContext().getBean(MainCtrl.class);
                                
                                // 跳转到主页面
                                Application.showView(MainView.class);
                                
                                // 视图切换完成后再刷新信息
                                if (mainCtrl != null) {
                                    // 给UI一点时间完成切换
                                    new Thread(() -> {
                                        try {
                                            Thread.sleep(300);
                                            Platform.runLater(() -> {
                                                // 直接调用公共方法刷新首页信息
                                                mainCtrl.refreshInfoAfterLogin();
                                            });
                                        } catch (InterruptedException e) {
                                            Thread.currentThread().interrupt();
                                        }
                                    }).start();
                                }
                            } catch (Exception e) {
                                log.error("自动登录切换视图失败", e);
                                showAlert("错误", "自动登录后切换界面失败");
                            }
                        });
                    } else {
                        log.info("Token已过期或无效，需要手动登录");
                        // token无效，清除token
                        appSettings.updateToken(null);
                    }
                }).start();
            }
        }
    }

    /**
     * 登录按钮点击事件
     */
    @FXML
    private void onLoginButtonClicked(ActionEvent event) {
        String username = usernameField.getText().trim();
        String password = passwordField.getText().trim();

        if (username.isEmpty() || password.isEmpty()) {
            showAlert("提示", "用户名和密码不能为空！");
            return;
        }

        // 禁用登录按钮，防止重复点击
        loginButton.setDisable(true);

        // 调用登录接口
        String loginMsg = apiService.login(username, password);

        if (StrUtil.isBlank(loginMsg)) {
            // 获取登录返回的token
            String token = apiService.getLoginToken();
            
            // 保存用户配置，包括token
            if (token != null && !token.isEmpty()) {
                appSettings.updateUserCredentials(username, password, token, rememberPasswordCheckBox.isSelected());
            } else {
                appSettings.updateUserCredentials(username, password, rememberPasswordCheckBox.isSelected());
            }
            
            loginButton.setDisable(false);
            // 使用Platform.runLater确保在UI线程中执行视图切换
            Platform.runLater(() -> {
                try {
                    // 先获取MainCtrl实例
                    MainCtrl mainCtrl = Application.getApplicationContext().getBean(MainCtrl.class);

                    // 跳转到主页面
                    Application.showView(MainView.class);

                    // 视图切换完成后再刷新信息
                    if (mainCtrl != null) {
                        // 给UI一点时间完成切换
                        new Thread(() -> {
                            try {
                                Thread.sleep(300);
                                Platform.runLater(() -> {
                                    // 直接调用公共方法刷新首页信息
                                    mainCtrl.refreshInfoAfterLogin();
                                });
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }).start();
                    }
                } catch (Exception e) {
                    log.error("切换视图失败", e);
                    // 恢复登录按钮状态
                    loginButton.setDisable(false);
                    showAlert("错误", "登录后切换界面失败");
                }
            });
        } else {
            // 恢复登录按钮状态
            loginButton.setDisable(false);
            showAlert("登录失败", loginMsg);
        }
    }
    
    /**
     * 注册按钮点击事件
     */
    @FXML
    private void onRegisterButtonClicked(ActionEvent event) {
        // 跳转到注册页面
        Application.showView(RegisterView.class);
    }
    
    /**
     * 处理查看公告按钮点击事件
     */
    @FXML
    public void onNoticeButtonClicked(ActionEvent event) {
        log.info("跳转到公告信息页面");
        Application.showView(NoticeView.class);
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }
} 