<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.text.Font?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.VBox?>

<BorderPane fx:id="rootPane" prefHeight="700.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.MainCtrl">
   <left>
      <VBox fx:id="sideMenu" alignment="TOP_CENTER" prefHeight="700.0" prefWidth="160.0" spacing="10.0" style="-fx-background-color: #3c3f41;" BorderPane.alignment="CENTER">
         <children>
            <Label fx:id="welcomeLabel" alignment="CENTER" prefWidth="140.0" text="欢迎使用系统" textAlignment="CENTER" textFill="WHITE">
               <font>
                  <Font size="16.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="20.0" top="20.0" />
               </VBox.margin>
            </Label>
            <Button fx:id="homeButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" text="首页">
               <VBox.margin>
                  <Insets left="10.0" right="10.0" />
               </VBox.margin>
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <Button fx:id="exchangeButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" text="激活码兑换">
               <VBox.margin>
                  <Insets left="10.0" right="10.0" />
               </VBox.margin>
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <Button fx:id="noticeButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" text="公告信息">
               <VBox.margin>
                  <Insets left="10.0" right="10.0" />
               </VBox.margin>
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <Button fx:id="changePasswordButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" text="修改密码">
               <VBox.margin>
                  <Insets left="10.0" right="10.0" />
               </VBox.margin>
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <Button fx:id="settingsButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" text="系统设置">
               <VBox.margin>
                  <Insets left="10.0" right="10.0" />
               </VBox.margin>
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <VBox alignment="BOTTOM_CENTER" prefHeight="350.0" prefWidth="100.0" VBox.vgrow="ALWAYS">
               <children>
                  <Button fx:id="logoutButton" maxWidth="1.7976931348623157E308" mnemonicParsing="false" prefHeight="40.0" style="-fx-background-color: #d32f2f;" text="登出" textFill="WHITE">
                     <VBox.margin>
                        <Insets bottom="20.0" left="10.0" right="10.0" />
                     </VBox.margin>
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Button>
               </children>
            </VBox>
         </children>
      </VBox>
   </left>
   <center>
      <Pane fx:id="contentPane" prefHeight="700.0" prefWidth="840.0" BorderPane.alignment="CENTER" />
   </center>
</BorderPane>
