package com.ceadeal.javafxboot.ctrl;

import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.service.ApiService;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.TextField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 兑换码控制器
 */
@Slf4j
@FXMLController
public class ExchangeCtrl implements Initializable {

    @FXML
    private TextField codeField;

    @FXML
    private Button exchangeButton;

    @Autowired
    private ApiService apiService;

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Exchange view initialized");
    }

    /**
     * 兑换按钮点击事件
     */
    @FXML
    private void onExchangeButtonClicked(ActionEvent event) {
        String code = codeField.getText().trim();
        
        if (code.isEmpty()) {
            showAlert("提示", "激活码不能为空！");
            return;
        }
        
        // 调用兑换接口
        CommonResult result = apiService.exchangeCode(code);
        
        if (result.getIsSuccess()) {
            showAlert("兑换成功", "激活码兑换成功！前往首页刷新查看最新到期时间，然后切换代理即可打开cursor使用了！");
            codeField.clear();
        } else {
            showAlert("兑换失败", result.getMsg());
        }
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }
} 