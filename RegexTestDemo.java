import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 正则表达式测试演示
 * 用于验证 AugmentHelper 中的正则表达式是否能正确匹配各种格式
 */
public class RegexTestDemo {

    public static void main(String[] args) {
        // 测试各种可能的 JavaScript 代码格式
        testRegexPatterns();
    }

    public static void testRegexPatterns() {
        // 测试用例 1：您提供的格式
        String testCase1 = "n={codeVerifier:t,codeChallenge:r,state:a,creationTime:new Date().getTime()};";
        
        // 测试用例 2：带空格的格式
        String testCase2 = "result = { codeVerifier: verifier, codeChallenge: challenge, state: stateValue, creationTime: new Date().getTime() };";
        
        // 测试用例 3：紧凑格式
        String testCase3 = "obj={codeVerifier:x,codeChallenge:y,state:z,creationTime:Date.now()}";
        
        // 测试用例 4：多行格式
        String testCase4 = "const result = {\n" +
                          "    codeVerifier: generatedVerifier,\n" +
                          "    codeChallenge: generatedChallenge,\n" +
                          "    state: generatedState,\n" +
                          "    creationTime: new Date().getTime()\n" +
                          "}";

        System.out.println("=== 正则表达式测试 ===\n");

        // 高级正则表达式（推荐使用）
        String advancedRegex = "([a-zA-Z_$][a-zA-Z0-9_$]*\\s*=\\s*)?\\{\\s*" +
                              "codeVerifier\\s*:\\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*,\\s*" +
                              "codeChallenge\\s*:\\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*,\\s*" +
                              "state\\s*:\\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*,\\s*" +
                              "creationTime\\s*:\\s*[^}]+\\}";

        // 基本正则表达式
        String basicRegex = "(\\{\\s*codeVerifier\\s*:\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\\s*,\\s*codeChallenge\\s*:\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\\s*,\\s*state\\s*:\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\\s*,\\s*creationTime\\s*:[^}]+\\})";

        String[] testCases = {testCase1, testCase2, testCase3, testCase4};
        String[] caseNames = {"您提供的格式", "带空格格式", "紧凑格式", "多行格式"};

        for (int i = 0; i < testCases.length; i++) {
            System.out.println("测试用例 " + (i + 1) + ": " + caseNames[i]);
            System.out.println("原始代码: " + testCases[i].replace("\n", "\\n"));
            
            // 测试高级正则表达式
            testPattern("高级正则", advancedRegex, testCases[i]);
            
            // 测试基本正则表达式
            testPattern("基本正则", basicRegex, testCases[i]);
            
            System.out.println("----------------------------------------\n");
        }
    }

    private static void testPattern(String patternName, String regex, String testString) {
        try {
            Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(testString);
            
            if (matcher.find()) {
                System.out.println("✅ " + patternName + " 匹配成功!");
                System.out.println("   完整匹配: " + matcher.group(0).replace("\n", "\\n"));
                
                if (patternName.equals("高级正则")) {
                    if (matcher.groupCount() >= 4) {
                        System.out.println("   变量赋值: " + (matcher.group(1) != null ? matcher.group(1) : "无"));
                        System.out.println("   codeVerifier变量: " + matcher.group(2));
                        System.out.println("   codeChallenge变量: " + matcher.group(3));
                        System.out.println("   state变量: " + matcher.group(4));
                    }
                } else if (patternName.equals("基本正则")) {
                    if (matcher.groupCount() >= 6) {
                        System.out.println("   codeVerifier变量: " + matcher.group(2));
                        System.out.println("   codeChallenge变量: " + matcher.group(4));
                        System.out.println("   state变量: " + matcher.group(6));
                    }
                }
                
                // 演示替换效果
                String replacement;
                if (patternName.equals("高级正则")) {
                    String varAssignment = matcher.group(1) != null ? matcher.group(1) : "";
                    replacement = varAssignment + "{" +
                                "codeVerifier:\"FIXED_VERIFIER\"," +
                                "codeChallenge:\"FIXED_CHALLENGE\"," +
                                "state:\"FIXED_STATE\"," +
                                "creationTime:new Date().getTime()}";
                } else {
                    replacement = matcher.group(1) + "\"FIXED_VERIFIER\"" +
                                matcher.group(3) + "\"FIXED_CHALLENGE\"" +
                                matcher.group(5) + "\"FIXED_STATE\"" +
                                matcher.group(7);
                }
                
                String result = testString.replace(matcher.group(0), replacement);
                System.out.println("   替换结果: " + result.replace("\n", "\\n"));
                
            } else {
                System.out.println("❌ " + patternName + " 匹配失败");
            }
        } catch (Exception e) {
            System.out.println("💥 " + patternName + " 发生异常: " + e.getMessage());
        }
    }
}
