/* 系统设置页面专用样式 */

/* 根容器样式 */
.settings-root {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* Tab面板样式优化 */
.tab-pane {
    -fx-background-color: transparent;
    -fx-border-width: 0;
}

.tab-pane .tab-header-area {
    -fx-background-color: #ffffff;
    -fx-border-color: transparent transparent #dee2e6 transparent;
    -fx-border-width: 0 0 2px 0;
    -fx-padding: 0 20px;
}

.tab-pane .tab {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-border-width: 0 0 3px 0;
    -fx-padding: 12px 24px;
    -fx-cursor: hand;
    -fx-background-radius: 0;
}

.tab-pane .tab:selected {
    -fx-background-color: transparent;
    -fx-border-color: transparent transparent #409EFF transparent;
    -fx-border-width: 0 0 3px 0;
}

.tab-pane .tab .tab-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 15px;
    -fx-font-weight: 500;
}

.tab-pane .tab:selected .tab-label {
    -fx-text-fill: #409EFF;
    -fx-font-weight: 600;
}

.tab-pane .tab:hover .tab-label {
    -fx-text-fill: #409EFF;
}

/* 卡片容器样式 */
.settings-card {
    -fx-background-color: #ffffff;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.06), 10, 0, 0, 2);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-padding: 24px;
}

.settings-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 15, 0, 0, 3);
    -fx-border-color: #dee2e6;
}

/* 卡片标题样式 */
.card-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #212529;
}

.card-description {
    -fx-font-size: 13px;
    -fx-text-fill: #6c757d;
    -fx-wrap-text: true;
    -fx-line-spacing: 2px;
}

/* 图标样式 */
.card-icon {
    -fx-font-size: 20px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

/* 输入框样式优化 */
.settings-text-field {
    -fx-background-color: #ffffff;
    -fx-background-insets: 0;
    -fx-background-radius: 6px;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 10px 12px;
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #adb5bd;
    -fx-effect: inset 0 1px 2px rgba(0,0,0,0.05);
}

.settings-text-field:focused {
    -fx-border-color: #409EFF;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(three-pass-box, rgba(64,158,255,0.25), 4, 0, 0, 0);
}

.settings-text-field:hover {
    -fx-border-color: #adb5bd;
}

/* 按钮样式优化 */
.settings-button {
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);
    -fx-border-width: 0;
}

/* 主要按钮 */
.settings-button.primary {
    -fx-background-color: linear-gradient(to bottom, #409EFF, #337ab7);
    -fx-text-fill: white;
}

.settings-button.primary:hover {
    -fx-background-color: linear-gradient(to bottom, #337ab7, #2e6da4);
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.settings-button.primary:pressed {
    -fx-background-color: linear-gradient(to bottom, #2e6da4, #245580);
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* 成功按钮 */
.settings-button.success {
    -fx-background-color: linear-gradient(to bottom, #67C23A, #5cb85c);
    -fx-text-fill: white;
}

.settings-button.success:hover {
    -fx-background-color: linear-gradient(to bottom, #5cb85c, #449d44);
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* 危险按钮 */
.settings-button.danger {
    -fx-background-color: linear-gradient(to bottom, #F56C6C, #d9534f);
    -fx-text-fill: white;
}

.settings-button.danger:hover {
    -fx-background-color: linear-gradient(to bottom, #d9534f, #c9302c);
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* 禁用状态 */
.settings-button:disabled {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #6c757d;
    -fx-effect: none;
    -fx-cursor: default;
}

/* 按钮组样式 */
.button-group {
    -fx-spacing: 12px;
}

/* 响应式间距 */
.settings-spacing-small {
    -fx-spacing: 8px;
}

.settings-spacing-medium {
    -fx-spacing: 16px;
}

.settings-spacing-large {
    -fx-spacing: 24px;
}

/* 状态指示器 */
.status-indicator {
    -fx-background-radius: 50%;
    -fx-min-width: 8px;
    -fx-min-height: 8px;
    -fx-max-width: 8px;
    -fx-max-height: 8px;
}

.status-indicator.active {
    -fx-background-color: #67C23A;
}

.status-indicator.inactive {
    -fx-background-color: #F56C6C;
}

.status-indicator.warning {
    -fx-background-color: #E6A23C;
}

/* 工具提示样式 */
.tooltip {
    -fx-background-color: rgba(0,0,0,0.8);
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
    -fx-padding: 6px 8px;
}

/* 滚动条样式 */
.scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 8px;
}

.scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
}

.scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 4px;
}

.scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #adb5bd;
}

/* 动画效果 */
.fade-in {
    -fx-opacity: 0;
}

.fade-in:hover {
    -fx-opacity: 1;
}
