.root {
    -fx-background-color: #f5f5f5;
}

.button {
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.button:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

#sideMenu {
    -fx-background-color: #304156;
}

#sideMenu .button {
    -fx-background-color: transparent;
    -fx-text-fill: #bfcbd9;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
    -fx-alignment: center-left;
    -fx-padding: 0 0 0 20;
}

#sideMenu .button:hover {
    -fx-background-color: #263445;
    -fx-text-fill: #ffffff;
}

#logoutButton {
    -fx-background-color: #f56c6c;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-alignment: center;
}

#logoutButton:hover {
    -fx-background-color: #f78989;
}

#contentPane {
    -fx-background-color: #f5f5f5;
}

.text-field, .password-field {
    -fx-background-color: white;
    -fx-border-color: #DCDFE6;
    -fx-border-radius: 4;
    -fx-padding: 8 12;
    -fx-prompt-text-fill: #909399;
}

.text-field:focused, .password-field:focused {
    -fx-border-color: #409EFF;
    -fx-effect: dropshadow(three-pass-box, rgba(64,158,255,0.2), 4, 0, 0, 0);
}

.label {
    -fx-text-fill: #606266;
}

.progress-bar {
    -fx-background-color: #E9E9EB;
    -fx-background-radius: 4;
    -fx-padding: 0;
}

.progress-bar > .bar {
    -fx-background-radius: 4;
}

/* Element UI主色调 */
.primary-color {
    -fx-text-fill: #409EFF;
}

.primary-bg {
    -fx-background-color: #409EFF;
}

.success-color {
    -fx-text-fill: #67C23A;
}

.success-bg {
    -fx-background-color: #67C23A;
}

.warning-color {
    -fx-text-fill: #E6A23C;
}

.warning-bg {
    -fx-background-color: #E6A23C;
}

.danger-color {
    -fx-text-fill: #F56C6C;
}

.danger-bg {
    -fx-background-color: #F56C6C;
}

.info-color {
    -fx-text-fill: #909399;
}

.info-bg {
    -fx-background-color: #909399;
}

/* Element文字颜色 */
.text-primary {
    -fx-text-fill: #303133;
}

.text-regular {
    -fx-text-fill: #606266;
}

.text-secondary {
    -fx-text-fill: #909399;
}

.text-placeholder {
    -fx-text-fill: #C0C4CC;
}

/* Element边框颜色 */
.border-base {
    -fx-border-color: #DCDFE6;
}

.border-light {
    -fx-border-color: #E4E7ED;
}

.border-lighter {
    -fx-border-color: #EBEEF5;
}

.border-extra-light {
    -fx-border-color: #F2F6FC;
}