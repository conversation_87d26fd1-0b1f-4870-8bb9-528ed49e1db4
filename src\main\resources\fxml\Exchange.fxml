<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox alignment="TOP_CENTER" prefWidth="840.0" prefHeight="700.0" spacing="20.0" style="-fx-background-color: #f5f5f5;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.ExchangeCtrl">
   <padding>
      <Insets top="40.0" right="40.0" bottom="40.0" left="40.0" />
   </padding>
   <children>
      <Label text="激活码兑换" textAlignment="CENTER" style="-fx-text-fill: #303133;">
         <font>
            <Font size="24.0" />
         </font>
         <VBox.margin>
            <Insets bottom="20.0" />
         </VBox.margin>
      </Label>
      <VBox alignment="CENTER" spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" maxWidth="500.0">
         <padding>
            <Insets top="30.0" right="30.0" bottom="30.0" left="30.0" />
         </padding>
         <children>
            <Label text="请输入您的激活码" textAlignment="CENTER" style="-fx-text-fill: #606266;">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <TextField fx:id="codeField" promptText="请输入激活码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets top="10.0" />
               </VBox.margin>
            </TextField>
            <Button fx:id="exchangeButton" defaultButton="true" mnemonicParsing="false" onAction="#onExchangeButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="激 活">
               <VBox.margin>
                  <Insets top="20.0" />
               </VBox.margin>
            </Button>
         </children>
      </VBox>
      <Label text="注意: 兑换码一经使用不可退换，请确保输入正确" style="-fx-text-fill: #909399;">
         <font>
            <Font size="12.0" />
         </font>
         <VBox.margin>
            <Insets top="20.0" />
         </VBox.margin>
      </Label>
   </children>
</VBox> 