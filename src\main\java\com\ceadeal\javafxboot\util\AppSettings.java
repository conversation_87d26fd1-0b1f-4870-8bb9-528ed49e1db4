package com.ceadeal.javafxboot.util;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import cn.hutool.crypto.SecureUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;

/**
 * 应用程序设置
 * 包含系统设置和用户登录信息
 */
@Slf4j
@Data
@Component
public class AppSettings {

    // 用户凭据
    private String username;
    private String password;
    private boolean remember;
    private String token;

    // Cursor配置
    private String vscdbPath;
    
    // Cursor安装路径
    private String cursorPath;
    
    // 谷歌浏览器路径
    private String chromePath;
    //租户ID
    private Long tenantId;

    private static final String CONFIG_FILE = "settings.properties";
    private static final String USERNAME_KEY = "username";
    private static final String PASSWORD_KEY = "password";
    private static final String REMEMBER_KEY = "remember";
    private static final String VSCDB_PATH_KEY = "vscdbPath";
    private static final String CURSOR_PATH_KEY = "cursorPath";
    private static final String CHROME_PATH_KEY = "chromePath";
    private static final String CURSOR_TOKEN_KEY = "cursorToken";
    private static final String TENANT_ID = "tenantId";
    private static final String TOKEN_KEY = "token";

    /**
     * 加载配置
     */
    public void loadConfig() {
        Properties prop = new Properties();
        File file = new File(CONFIG_FILE);

        if (file.exists()) {
            try (FileInputStream fis = new FileInputStream(file)) {
                prop.load(fis);

                // 加载用户凭据
                username = prop.getProperty(USERNAME_KEY);
                String encryptedPassword = prop.getProperty(PASSWORD_KEY);
                if (encryptedPassword != null && !encryptedPassword.isEmpty()) {
                    password = encryptedPassword; // 密码已加密，保持加密状态
                }

                // 加载token
                token = prop.getProperty(TOKEN_KEY);

                String rememberStr = prop.getProperty(REMEMBER_KEY);
                if (rememberStr != null) {
                    remember = Boolean.parseBoolean(rememberStr);
                }

                String tenantIdStr = prop.getProperty(TENANT_ID);
                if (tenantIdStr != null) {
                    tenantId = Long.parseLong(tenantIdStr);
                }
                log.info("已加载应用设置: tenantId={}", tenantId);
                // 加载Cursor配置
                vscdbPath = prop.getProperty(VSCDB_PATH_KEY);
                
                // 加载Cursor安装路径
                cursorPath = prop.getProperty(CURSOR_PATH_KEY);

                // 加载谷歌浏览器路径
                chromePath = prop.getProperty(CHROME_PATH_KEY);

                log.info("已加载应用设置: remember={}", remember);
            } catch (Exception e) {
                log.error("加载应用设置出错", e);
            }
        } else {
            // 配置文件不存在，使用默认值并创建文件
            saveConfig();
        }

        // 如果vscdbPath为空，尝试获取默认路径
        if (vscdbPath == null || vscdbPath.isEmpty()) {
            initVscdbPath();
        }
        
        // 如果cursorPath为空，尝试设置默认路径
        if (cursorPath == null || cursorPath.isEmpty()) {
            initCursorPath();
        }
        
        // 如果chromePath为空，尝试设置默认路径
        if (chromePath == null || chromePath.isEmpty()) {
            initChromePath();
        }
    }

    /**
     * 初始化Cursor安装路径
     */
    public void initCursorPath() {
        // 尝试常见的安装位置
        Set<String> possiblePaths = new HashSet<>();
        
        // 系统默认位置
        String path1 = System.getenv("ProgramFiles") + "\\Cursor";
        possiblePaths.add(path1);
        String path2 = System.getenv("ProgramFiles(x86)") + "\\Cursor";
        possiblePaths.add(path2);
        String path3 = System.getenv("LocalAppData") + "\\Programs\\cursor";
        possiblePaths.add(path3);
        
        // 不同盘符上的常见安装位置
        String[] drives = {"C", "D", "E"};

        for (String drive : drives) {
           //替换path1-3的第一个字符即盘符为 drive
            if (StrUtil.isNotBlank(path1)) {
                possiblePaths.add(drive + StrUtil.sub(path1, 1, StrUtil.length(path1)));
            }
            if (StrUtil.isNotBlank(path2)) {
                possiblePaths.add(drive + StrUtil.sub(path2, 1, StrUtil.length(path2)));
            }
            if (StrUtil.isNotBlank(path3)) {
                possiblePaths.add(drive + StrUtil.sub(path3, 1, StrUtil.length(path3)));
            }
        }
        
        for (String path : possiblePaths) {
            try {
                File exeFile = new File(path + "\\Cursor.exe");
                if (exeFile.exists()) {
                    this.cursorPath = path;
                    saveConfig();
                    log.info("找到Cursor安装路径: {}", path);
                    return;
                }
            } catch (Exception e) {
                // 忽略无效路径或存在权限问题的路径
                log.debug("检查路径失败: {}, 原因: {}", path, e.getMessage());
            }
        }
        
        log.info("未找到Cursor安装路径，请手动设置");
    }

    /**
     * 初始化谷歌浏览器路径
     */
    public void initChromePath() {
        String defaultPath = "";
        // 根据操作系统设置默认路径
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            // Windows默认路径
            File chrome = new File("C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe");
            if (chrome.exists()) {
                defaultPath = chrome.getAbsolutePath();
            } else {
                chrome = new File("C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe");
                if (chrome.exists()) {
                    defaultPath = chrome.getAbsolutePath();
                }
            }
        } else if (os.contains("mac")) {
            // Mac默认路径
            File chrome = new File("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome");
            if (chrome.exists()) {
                defaultPath = chrome.getAbsolutePath();
            }
        } else if (os.contains("linux")) {
            // Linux默认路径，通常在/usr/bin/google-chrome
            File chrome = new File("/usr/bin/google-chrome");
            if (chrome.exists()) {
                defaultPath = chrome.getAbsolutePath();
            }
        }
        
        if (!defaultPath.isEmpty()) {
            this.chromePath = defaultPath;
            saveConfig();
        }
    }

    /**
     * 初始化vscdb路径
     */
    public void initVscdbPath() {
        String fullPath = CursorHelper.getVscdbPath();
        if (fullPath != null && !fullPath.isEmpty()) {
            // 去掉路径中的"User/globalStorage/state.vscdb"部分
            String basePath = fullPath;
            int userIndex = fullPath.lastIndexOf("User");
            if (userIndex > 0) {
                basePath = fullPath.substring(0, userIndex);
            }
            this.vscdbPath = basePath;
            saveConfig();
        }
    }

    /**
     * 获取完整的vscdb路径
     */
    public String getFullVscdbPath() {
        if (vscdbPath == null || vscdbPath.isEmpty()) {
            return CursorHelper.getVscdbPath();
        }
        return vscdbPath + "User" + File.separator + "globalStorage" + File.separator + "state.vscdb";
    }

    /**
     * 保存配置
     */
    public void saveConfig() {
        Properties prop = new Properties();

        // 保存用户凭据
        if (username != null) {
            prop.setProperty(USERNAME_KEY, username);
        }

        if (remember && password != null) {
            prop.setProperty(PASSWORD_KEY, password);
        } else {
            prop.setProperty(PASSWORD_KEY, "");
        }

        // 保存token
        if (token != null) {
            prop.setProperty(TOKEN_KEY, token);
        }

        prop.setProperty(REMEMBER_KEY, String.valueOf(remember));
        prop.setProperty(TENANT_ID, String.valueOf(tenantId));

        // 保存Cursor配置
        if (vscdbPath != null) {
            prop.setProperty(VSCDB_PATH_KEY, vscdbPath);
        }
        
        // 保存Cursor安装路径
        if (cursorPath != null) {
            prop.setProperty(CURSOR_PATH_KEY, cursorPath);
        }
        
        // 保存谷歌浏览器路径
        if (chromePath != null) {
            prop.setProperty(CHROME_PATH_KEY, chromePath);
        }

        try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
            prop.store(fos, "Cursor Agent Settings");
            log.info("已保存应用设置: remember={}", remember);
        } catch (Exception e) {
            log.error("保存应用设置出错", e);
        }
    }

    /**
     * 清除配置
     */
    public void clearConfig() {
        this.username = null;
        this.password = null;
        this.remember = false;
        this.token = null;
        // 保存空配置
        saveConfig();
    }

    /**
     * 兼容旧版本的loadSettings方法
     */
    public void loadSettings() {
        loadConfig();
    }

    /**
     * 兼容旧版本的saveSettings方法
     */
    public void saveSettings() {
        saveConfig();
    }

    /**
     * 更新用户凭据
     */
    public void updateUserCredentials(String username, String password, boolean remember) {
        this.username = username;
        this.password = password;
        this.remember = remember;
        saveConfig();
    }

    /**
     * 更新用户凭据，包括token
     */
    public void updateUserCredentials(String username, String password, String token, boolean remember) {
        this.username = username;
        this.password = password;
        this.token = token;
        this.remember = remember;
        saveConfig();
    }

    /**
     * 更新Cursor vscdb路径
     */
    public void updateVscdbPath(String path) {
        this.vscdbPath = path;
        saveConfig();
    }
    
    /**
     * 更新Cursor安装路径
     */
    public void updateCursorPath(String path) {
        this.cursorPath = path;
        saveConfig();
    }
    
    /**
     * 更新谷歌浏览器路径
     */
    public void updateChromePath(String path) {
        this.chromePath = path;
        saveConfig();
    }

    /**
     * 更新token
     */
    public void updateToken(String token) {
        this.token = token;
        saveConfig();
    }

    /**
     * 检查是否有有效的token
     */
    public boolean hasValidToken() {
        return token != null && !token.isEmpty();
    }

    /**
     * 加密密码（目前使用MD5，可以根据需要改为可逆加密）
     */
    public String encryptPassword(String plainPassword) {
        return SecureUtil.md5(plainPassword);
    }
}