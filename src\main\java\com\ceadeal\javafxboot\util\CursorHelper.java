package com.ceadeal.javafxboot.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.ConsoleHandler;
import java.util.logging.LogRecord;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;

public class CursorHelper {
    private static final Logger logger = Logger.getLogger(CursorHelper.class.getName());

    static {
        // 配置日志格式
        ConsoleHandler handler = new ConsoleHandler();
        handler.setFormatter(new SimpleFormatter() {
            @Override
            public String format(LogRecord record) {
                return String.format("[%s] %s: %s%n",
                        new Date(record.getMillis()),
                        record.getLevel(),
                        record.getMessage());
            }
        });
        logger.addHandler(handler);
        logger.setUseParentHandlers(false);
    }

    /**
     * 更新服务器配置，将bugBotV1的enable值修改为false
     * 
     * @param dbPath 数据库路径
     * @return 更新是否成功
     */
    public static boolean updateServerConfig(String dbPath) {
        if (StrUtil.isBlank(dbPath)) {
            dbPath = getVscdbPath();
        } else {
            dbPath = dbPath + "User\\globalStorage\\state.vscdb";
        }
        String url = "jdbc:sqlite:" + dbPath;

        try (Connection conn = DriverManager.getConnection(url)) {
            // 查询cursorai/serverConfig的值
            String query = "SELECT value FROM itemTable WHERE key = 'cursorai/serverConfig'";
            String configValue = null;
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(query)) {
                if (rs.next()) {
                    configValue = rs.getString("value");
                }
            }
            
            // 如果配置值不为空，则修改bugBotV1的enable值
            if (StrUtil.isNotBlank(configValue)) {
                JSONObject jsonObject = JSONUtil.parseObj(configValue);
                JSONObject bugConfigResponse = jsonObject.getJSONObject("bugConfigResponse");
                // 检查是否存在bugBotV1配置
                if (bugConfigResponse != null && bugConfigResponse.containsKey("bugBotV1")) {
                    JSONObject bugBotConfig = bugConfigResponse.getJSONObject("bugBotV1");
                    if (bugBotConfig != null) {
                        // 设置enable为false
                        bugBotConfig.put("enabled", false);
                        bugBotConfig.put("backgroundCallFrequencyMs", 3600000000L); //检测频率
                        bugBotConfig.put("thresholdForExpensiveRunModalCents", 100000); //触发高成本分析的金额阈值
                        bugBotConfig.put("expensiveAbsoluteMaxTokens", 1500000000); //高成本模型最大 token 数
                        bugBotConfig.put("errorRateLimit", 0.001); //
                        bugBotConfig.put("performanceUnitRateLimit", 0.001); //
                        jsonObject.put("bugBotV1", bugBotConfig);

                        // 更新数据库中的配置
                        String updateSql = "UPDATE itemTable SET value = ? WHERE key = 'cursorai/serverConfig'";
                        try (PreparedStatement pstmt = conn.prepareStatement(updateSql)) {
                            pstmt.setString(1, jsonObject.toString());
                            int updated = pstmt.executeUpdate();
                            if (updated > 0) {
                                logger.info("成功更新bugBotV1配置，已禁用");
                                return true;
                            }
                        }
                    }
                }
            } else {
                logger.warning("未找到服务器配置信息");
            }
            
            return false;
        } catch (SQLException e) {
            logger.severe("更新服务器配置异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 重置机器码
     *
     * @return
     */
    public static boolean resetMachineCode(String vscdbPath, String cursorPath) {
        String os = System.getProperty("os.name").toLowerCase();
        logger.info("当前操作系统: " + os);
        boolean flag = false;
        try {
//            if (os.contains("mac") || os.contains("darwin")) {
//                flag= executeMacCommand("curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_mac_id_modifier.sh -o ./cursor_mac_id_modifier.sh && sudo bash ./cursor_mac_id_modifier.sh && rm ./cursor_mac_id_modifier.sh");
//            } else if (os.contains("win")) {
//                //HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography 下的MachineGuid
////                flag= executeWindowsCommand("irm https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_win_id_modifier.ps1 | iex");
////                flag= executeWindowsCommand("\"$env:APPDATA = 'C:\\\\MyCustomPath'; irm http://*************/resetMachineCode.ps1 | iex");
//
//                String command = "irm http://*************/resetMachineCode.ps1 | iex";
//                if (StrUtil.isNotBlank(vscdbPath)) {
//                    command = "$env:APPDATA = '" + vscdbPath.replace("\\Cursor\\", "") + "'; "+command;
//                }
//                if (StrUtil.isNotBlank(cursorPath)) {
//                    command = "$env:LOCALAPPDATA = '" + cursorPath.replace("\\Programs\\cursor\\", "").replace("\\Programs\\cursor", "").replace("\\cursor", "") + "'; " + command;
//                }
//                flag= executeWindowsCommand(command);
//
//            } else {
//                //linux和其它系统
//                flag= executeLinuxCommand("curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_linux_id_modifier.sh | sudo bash");
//            }
//            if (flag) {
//                logger.info("重置机器码成功");
//            } else {
//                if (StrUtil.isNotBlank(vscdbPath) || StrUtil.isNotBlank(cursorPath)) {
//                    //不传再来重置一次
//                    return resetMachineCode(null, null);
//                }
//                logger.severe("重置机器码失败");
//            }
        } catch (Exception e) {
            logger.severe("重置机器码异常: " + e.getMessage());
            if (StrUtil.isNotBlank(vscdbPath) || StrUtil.isNotBlank(cursorPath)) {
                //不传再来重置一次
                return resetMachineCode(null, null);
            }
        } finally {
            // 无论重置成功还是失败，都执行本地的reset.ps1脚本
            if (os.contains("win")) {
                executeLocalResetScript(vscdbPath);
            }
        }
        return flag;
    }
    private static boolean executeMacCommand(String command) throws IOException, InterruptedException {
        logger.info("执行macOS命令");
        return executeCommand(new String[]{"bash", "-c", command});
    }

    private static boolean executeLinuxCommand(String command) throws IOException, InterruptedException {
        logger.info("执行Linux命令");
        return executeCommand(new String[]{"bash", "-c", command});
    }

    public static boolean executeWindowsCommand(String command) throws IOException, InterruptedException {
        logger.info("执行Windows命令");
        return executeCommand(new String[]{"powershell", "-Command", command});
    }

    private static boolean executeCommand(String[] command) throws IOException, InterruptedException {
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()))) {
            writer.write("1\n");  // 提供确认输入
            writer.flush();
        }
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
//                logger.info(line);
            }
        }

        int exitCode = process.waitFor();
        return exitCode == 0;
    }

    /**
     * 执行本地的reset.ps1脚本
     *
     * @param vscdbPath 数据库路径，用于设置$env:APPDATA
     */
    private static void executeLocalResetScript(String vscdbPath) {
        try {
            // 获取reset.ps1脚本的路径
            InputStream scriptStream = CursorHelper.class.getResourceAsStream("/script/reset.ps1");
            if (scriptStream == null) {
                logger.warning("未找到reset.ps1脚本文件");
                return;
            }

            // 读取脚本内容
            StringBuilder scriptContent = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(scriptStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    scriptContent.append(line).append("\n");
                }
            }

            // 构建PowerShell命令
            String command;
            if (StrUtil.isNotBlank(vscdbPath)) {
                // 设置$env:APPDATA环境变量，与resetMachineCode.ps1的逻辑一样
                String appDataPath = vscdbPath.replace("\\Cursor\\", "");
                command = "$env:APPDATA = '" + appDataPath + "'; " + scriptContent.toString();
            } else {
                command = scriptContent.toString();
            }

            logger.info("开始执行本地reset.ps1脚本");
            boolean success = executeWindowsCommand(command);
            if (success) {
                logger.info("本地reset.ps1脚本执行成功");
            } else {
                logger.warning("本地reset.ps1脚本执行失败");
            }
        } catch (Exception e) {
            logger.severe("执行本地reset.ps1脚本异常: " + e.getMessage());
        }
    }


    // 获取不同操作系统下的 .vscdb 文件路径
    public static String getVscdbPath() {
        String os = System.getProperty("os.name").toLowerCase();
        Path path;

        if (os.contains("win")) {
            // Windows
            String appData = System.getenv("APPDATA");
            path = Paths.get(appData, "Cursor", "User", "globalStorage", "state.vscdb");
        } else if (os.contains("mac")) {
            // macOS
            path = Paths.get(System.getProperty("user.home"),
                    "Library", "Application Support",
                    "Cursor", "User", "globalStorage", "state.vscdb");
        } else {
            // Linux 和其他 Unix 系统
            path = Paths.get(System.getProperty("user.home"),
                    ".config", "Cursor",
                    "User", "globalStorage", "state.vscdb");
        }

        String pathString = path.toString();
        System.out.println("cursor安装路径" + pathString.replace("state.vscdb", ""));
        return pathString;
    }

    // 读取认证信息
    public static Map<String, Object> readAuthInfo(String dbPath) {
        if (StrUtil.isBlank(dbPath)) {
             dbPath = getVscdbPath();
        }else{
            dbPath = dbPath + "User\\globalStorage\\state.vscdb";
        }
//        System.out.println("数据库路径: " + dbPath);

        // JDBC 连接字符串
        String url = "jdbc:sqlite:" + dbPath;
        Map<String, Object> resultMap = new HashMap<>();
        try (Connection conn = DriverManager.getConnection(url)) {
//            System.out.println("成功连接到数据库");

            // 查询所有表
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table'")) {
//                System.out.println("数据库中的表:");
                while (rs.next()) {
//                    System.out.println(rs.getString("name"));
                }
            }

            // 查询认证信息
            String query = "SELECT key, value FROM itemTable WHERE key LIKE 'cursorAuth/%' or key ='cursorai/serverConfig'";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(query)) {
//                System.out.println("\n认证信息:");
                while (rs.next()) {
                    String key = rs.getString("key");
                    String value = rs.getString("value");
//                    System.out.printf("%s = %s%n", key.substring(key.lastIndexOf('/') + 1), value);
                    resultMap.put(key, value);
                }
            }
//            //查询表所有数据
//            query = "SELECT * FROM itemTable";
//            try (Statement stmt = conn.createStatement();
//                 ResultSet rs = stmt.executeQuery(query)) {
//                while (rs.next()) {
//                    //打印所有字段信息
//                    for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
//                        System.out.printf("%s = %s%n", rs.getMetaData().getColumnName(i), rs.getString(i));
//                    }
//                }
//            }
        } catch (SQLException e) {
            System.err.println("cursor token更新异常，请检查cursor应用数据路径");
        }
        return resultMap;
    }

    public static boolean updateAuthInfo(String email, String accessToken, String refreshToken, String dbPath) {
        if (StrUtil.isBlank(dbPath)) {
            dbPath = getVscdbPath();
        }else{
            dbPath = dbPath + "User\\globalStorage\\state.vscdb";
        }
        String url = "jdbc:sqlite:" + dbPath;

        try (Connection conn = DriverManager.getConnection(url)) {
            conn.setAutoCommit(false); // 开启事务

            // 更新或插入认证信息
            updateOrInsert(conn, "cursorAuth/cachedSignUpType", "Auth_0");
            if (email != null) updateOrInsert(conn, "cursorAuth/cachedEmail", email);
            if (accessToken != null) updateOrInsert(conn, "cursorAuth/accessToken", accessToken);
            if (refreshToken != null) updateOrInsert(conn, "cursorAuth/refreshToken", refreshToken);
            updateOrInsert(conn, "cursorAuth/stripeMembershipType", "free_trial");
            updateOrInsert(conn, "cursorAuth/onboardingDate", getUTCDate());

            // 删除 cursorai/serverConfig 键
            deleteKey(conn, "cursorai/serverConfig");

            conn.commit();
            System.out.println("更新cursor 邮箱成功");
            return true;
        } catch (SQLException e) {
            System.err.println("更新cursor异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从数据库中删除指定的键
     * 
     * @param conn 数据库连接
     * @param key 要删除的键名
     */
    private static void deleteKey(Connection conn, String key) {
        try {
            String deleteSql = "DELETE FROM itemTable WHERE key = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {
                pstmt.setString(1, key);
                int deleted = pstmt.executeUpdate();
                if (deleted > 0) {
                    System.out.println("删除键 " + key + " 成功");
                }
            }
        } catch (SQLException e) {
            System.err.println("删除键 " + key + " 异常: " + e.getMessage());
            // 继续执行，不影响主流程
        }
    }

    private static void updateOrInsert(Connection conn, String key, String value) throws SQLException {
        // 先尝试更新
        String updateSql = "UPDATE itemTable SET value = ? WHERE key = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(updateSql)) {
            pstmt.setString(1, value);
            pstmt.setString(2, key);
            int updated = pstmt.executeUpdate();

            if (updated == 0) {
                // 如果没有更新任何行，则插入新记录
                String insertSql = "INSERT INTO itemTable (key, value) VALUES (?, ?)";
                try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                    insertStmt.setString(1, key);
                    insertStmt.setString(2, value);
                    insertStmt.executeUpdate();
//                    System.out.println("新增: " + key);
                }
            } else {
//                System.out.println("更新: " + key);
            }
        }
    }

    public static void main(String[] args) {
//        //获取昨天时间 2025-05-29T09:53:47.492Z格式
//        String result = getUTCDate();
//        System.out.println(result);
////                .toString("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        String email = "<EMAIL>";
        String accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzBFWkVERURKNENZM0FFRDQzMFdUTUcxIiwidGltZSI6IjE3NTI4NDk0NzkiLCJyYW5kb21uZXNzIjoiZWQ5Y2Y4NGMtMmU4ZS00ODg1IiwiZXhwIjoxNzU4MDMzNDc5LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.3m9J6UWV-DLyHpWoJOl_QCwQhwB-UW56pbnFBrdQ3yI";
        String refreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzBFWkVERURKNENZM0FFRDQzMFdUTUcxIiwidGltZSI6IjE3NTI4NDk0NzkiLCJyYW5kb21uZXNzIjoiZWQ5Y2Y4NGMtMmU4ZS00ODg1IiwiZXhwIjoxNzU4MDMzNDc5LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.3m9J6UWV-DLyHpWoJOl_QCwQhwB-UW56pbnFBrdQ3yI";
//        HttpRequest httpRequest = HttpUtil.createPost("https://www.cursor.com/api/dashboard/get-filtered-usage-events");
////        HttpRequest httpRequest = HttpUtil.createGet("https://www.cursor.com/api/usage?uid=user_01JXB780E21TJF7HDD4QNEV7JY");
//        httpRequest.cookie("WorkosCursorSessionToken=" + "user_01JXB780E21TJF7HDD4QNEV7JY%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlhCNzgwRTIxVEpGN0hERDRRTkVWN0pZIiwidGltZSI6IjE3NDk1MDIyNjYiLCJyYW5kb21uZXNzIjoiMjI4YzZhYWQtYWFjOS00NDVlIiwiZXhwIjoxNzU0Njg2MjY2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.5rP1dX6iCLs9SYfidCME4LxACnu7mPFoAdw6otVzJAI");
//        JSONObject requestParams = new JSONObject();
//        requestParams.put("page", 1);
//        requestParams.put("pageSize", 1);
//        httpRequest.body(requestParams.toString());
//
//        // 设置超时
//        httpRequest.setConnectionTimeout(10000); // 10秒连接超时
//        httpRequest.setReadTimeout(15000);      // 15秒读取超时
//
//        String body = httpRequest.execute().body();
//        if (StrUtil.isBlank(body)) {
//            System.out.println("请求失败");
//            return;
//        }
//        JSONObject jsonObject = JSONUtil.parseObj(body);
//        System.out.println(jsonObject);
//        System.out.println("使用量：" + jsonObject.getStr("totalUsageEventsCount"));

//        String accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlZFMFBTS01RSFowOVBXV1QzTkRRRUc0IiwidGltZSI6IjE3NDc0Njc5ODkiLCJyYW5kb21uZXNzIjoiMzUyMzJhMTEtYjMzMS00NDhjIiwiZXhwIjoxNzUyNjUxOTg5LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.THX4u3ekKs9aFDCEpF-TxibMAroe-ledE-0byKJJbrk";
//        String refreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlZFMFBTS01RSFowOVBXV1QzTkRRRUc0IiwidGltZSI6IjE3NDc0Njc5ODkiLCJyYW5kb21uZXNzIjoiMzUyMzJhMTEtYjMzMS00NDhjIiwiZXhwIjoxNzUyNjUxOTg5LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.THX4u3ekKs9aFDCEpF-TxibMAroe-ledE-0byKJJbrk";
//        String accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlcxTUdROFozNkEyVEpITjc5VDM3MzJEIiwidGltZSI6IjE3NDgxMDY3OTgiLCJyYW5kb21uZXNzIjoiN2ZlMGYwOGUtYzVmMi00YTg2IiwiZXhwIjoxNzUzMjkwNzk4LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.nUeL8NmWF345YmzY0uI7KZRWGk5kL2OW8unV7vf0rJc";
//        String refreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlcxTUdROFozNkEyVEpITjc5VDM3MzJEIiwidGltZSI6IjE3NDgxMDY3OTgiLCJyYW5kb21uZXNzIjoiN2ZlMGYwOGUtYzVmMi00YTg2IiwiZXhwIjoxNzUzMjkwNzk4LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.nUeL8NmWF345YmzY0uI7KZRWGk5kL2OW8unV7vf0rJc";
//        updateServerConfig(null);
        updateAuthInfo(email, accessToken, refreshToken,null);
//        Map<String, Object> map = readAuthInfo(null);
//        for (String key : map.keySet()) {
//            System.out.println(key + " ： " + map.get(key));
//        }

//        JWT jwt = JWT.of(accessToken);
//        Object sub = jwt.getPayload("sub");
//        if (sub != null) {
//            String uid = sub.toString().replace("auth0|", "");
//            HttpRequest httpRequest = HttpUtil.createGet("https://www.cursor.com/api/usage?uid=" + uid);
//            httpRequest.cookie("WorkosCursorSessionToken=" + uid + "%3A%3A" + accessToken);
//            String body = httpRequest.execute().body();
//            JSONObject jsonObject = JSONUtil.parseObj(body).getJSONObject("gpt-4");
//            String numRequests = jsonObject.getStr("numRequests");
//            System.out.println("使用额度:" + numRequests);
//        }
//        updateAuthInfo("<EMAIL>", "11", "22");
//        System.out.println(resetMachineCode());
//        System.out.println(getVscdbPath());
//
//        String os = System.getProperty("os.name").toLowerCase();
//        Path path;
//
//        if (os.contains("win")) {
//            // Windows
//            String appData = System.getenv("APPDATA");
//            path = Paths.get(appData, "Cursor");
//            System.out.println(path.toString());
//        }
        //解析jwt token

    }

    private static String getUTCDate() {
        DateTime date = DateUtil.offset(DateUtil.date(), DateField.DAY_OF_YEAR, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String result = sdf.format(date); //        DateUtil.offset(DateUtil.date(), DateField.DAY_OF_YEAR, -1).setTimeZone(TimeZone.getTimeZone("UTC"))
        return result;
    }
}
