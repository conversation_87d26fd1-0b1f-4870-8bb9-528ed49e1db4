<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.4.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.ceadeal</groupId>
    <artifactId>javafx-boot</artifactId>
    <version>1.0</version>
    <name>javafx-boot</name>
    <description>JavaFx with Spring Boot</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>
    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.microsoft.playwright</groupId>-->
<!--            <artifactId>playwright</artifactId>-->
<!--            <version>1.42.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
            <version>5.8.32</version>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>********</version> <!-- 使用最新版本 -->
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.openjfx</groupId>-->
<!--            <artifactId>javafx-controls</artifactId>-->
<!--            <version>17.0.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.openjfx</groupId>-->
<!--            <artifactId>javafx-fxml</artifactId>-->
<!--            <version>17.0.1</version>-->
<!--        </dependency>-->
            <dependency>
                <groupId>org.kordamp.bootstrapfx</groupId>
                <artifactId>bootstrapfx-core</artifactId>
                <version>0.3.0</version>
            </dependency>
<!--        <dependency>-->
<!--            <groupId>org.controlsfx</groupId>-->
<!--            <artifactId>controlsfx</artifactId>-->
<!--            <version>8.40.18</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>de.roskenet</groupId>
            <artifactId>springboot-javafx-support</artifactId>
            <version>2.1.6</version>
        </dependency>
        <!--<dependency>
            <groupId>de.jensd</groupId>
            <artifactId>fontawesomefx</artifactId>
            <version>8.9</version>
        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.32</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.zenjava</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>8.8.3</version>
                <configuration>
                    <!-- 启动类 -->
                    <mainClass>com.ceadeal.javafxboot.Application</mainClass>
                    <!-- 公司名称 -->
                    <vendor>抖音搜码大师</vendor>
                    <!-- 应用名称 ${project.build.finalName} = ${project.artifactId}-${project.version} -->
                    <appName>${project.build.finalName}</appName>
                    <!-- 发行版本 -->
                    <nativeReleaseVersion>${project.version}</nativeReleaseVersion>
                    <!--
                        图标设置

                        > 参考：https://stackoverflow.com/questions/15880102/how-to-set-custom-icon-for-javafx-native-package-icon-on-windows

                        # 方式1（按deployDir、appName配置读取ico文件）
                        # 使用jfx:native打包时，默认会去src/main/deploy/package/windows/${appName}.ico
                        <deployDir>${project.basedir}/src/main/deploy</deployDir>

                        # 方式2（固定使用一个图标，与版本号无关）
                        # 优先级高于第一种方式
                        <bundleArguments>
                            <icon>${project.basedir}/src/main/resources/icon/icon.ico</icon>
                        </bundleArguments>
                     -->
                    <bundleArguments>
                        <icon>${project.basedir}/src/main/resources/icon/icon.ico</icon>
                    </bundleArguments>
                    <!-- 桌面图标 -->
                    <needShortcut>true</needShortcut>
                    <!-- 菜单设置 -->
                    <needMenu>true</needMenu>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>