<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="400.0" prefWidth="600.0" style="-fx-background-color: #f5f5f5;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.RegisterCtrl">
   <children>
      <VBox alignment="CENTER" layoutX="150.0" layoutY="50.0" prefHeight="300.0" prefWidth="340.0" spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" AnchorPane.bottomAnchor="50.0" AnchorPane.leftAnchor="130.0" AnchorPane.rightAnchor="130.0" AnchorPane.topAnchor="50.0">
         <padding>
            <Insets bottom="30.0" left="20.0" right="20.0" top="30.0" />
         </padding>
         <children>
            <Label text="用户注册" textAlignment="CENTER" style="-fx-text-fill: #303133;">
               <font>
                  <Font size="24.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="20.0" />
               </VBox.margin>
            </Label>
            <TextField fx:id="usernameField" promptText="请输入用户名" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets bottom="10.0" />
               </VBox.margin>
            </TextField>
            <PasswordField fx:id="passwordField" promptText="请输入密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets bottom="10.0" />
               </VBox.margin>
            </PasswordField>
            <PasswordField fx:id="confirmPasswordField" promptText="请确认密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets bottom="20.0" />
               </VBox.margin>
            </PasswordField>
            <HBox alignment="CENTER" spacing="20.0">
               <children>
                  <Button fx:id="registerButton" defaultButton="true" mnemonicParsing="false" onAction="#onRegisterButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="注 册">
                     <HBox.margin>
                        <Insets />
                     </HBox.margin>
                  </Button>
                  <Button fx:id="backToLoginButton" mnemonicParsing="false" onAction="#onBackToLoginButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #909399; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="返回登录">
                     <HBox.margin>
                        <Insets />
                     </HBox.margin>
                  </Button>
               </children>
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane> 