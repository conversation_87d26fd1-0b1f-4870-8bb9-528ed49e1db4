<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<VBox alignment="TOP_CENTER" prefWidth="840.0" prefHeight="700.0" spacing="20.0" style="-fx-background-color: #f5f5f5;"
      xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.NoticeCtrl">
   <padding>
      <Insets top="40.0" right="40.0" bottom="40.0" left="40.0" />
   </padding>
   <children>
      <Label text="公告信息" textAlignment="CENTER" style="-fx-text-fill: #303133;">
         <font>
            <Font size="24.0" />
         </font>
         <VBox.margin>
            <Insets bottom="20.0" />
         </VBox.margin>
      </Label>
      
      <!-- 顶部操作栏 -->
      <HBox spacing="10.0" alignment="CENTER">
         <Button fx:id="backButton" text="返回登录" onAction="#backToLogin" style="-fx-background-color: #67C23A; -fx-text-fill: white;">
            <HBox.margin>
               <Insets right="20.0" />
            </HBox.margin>
         </Button>
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="refreshButton" text="刷新" onAction="#refreshNotice" style="-fx-background-color: #409EFF; -fx-text-fill: white;" />
      </HBox>

      <!-- 公告信息区块 -->
      <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
         <padding>
            <Insets top="20.0" right="20.0" bottom="20.0" left="20.0" />
         </padding>
         <children>
            <Label text="公告信息" style="-fx-font-weight: bold; -fx-text-fill: #303133;">
               <font>
                  <Font size="18.0" />
               </font>
            </Label>
            <TextArea fx:id="noticeContentText" text="正在加载公告信息..." style="-fx-fill: #606266;" wrapText="true" editable="false" VBox.vgrow="ALWAYS" prefHeight="200.0" />
         </children>
      </VBox>

      <!-- 使用须知区块 -->
      <VBox alignment="TOP_LEFT" spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
         <padding>
            <Insets top="20.0" right="20.0" bottom="20.0" left="20.0" />
         </padding>
         <children>
            <Label text="使用须知" style="-fx-font-weight: bold; -fx-text-fill: #303133;">
               <font>
                  <Font size="18.0" />
               </font>
            </Label>
            <TextArea fx:id="helpContentText" text="正在加载使用须知..." style="-fx-fill: #606266;" wrapText="true" editable="false" VBox.vgrow="ALWAYS" prefHeight="200.0" />
         </children>
      </VBox>
         </children>
</VBox> 