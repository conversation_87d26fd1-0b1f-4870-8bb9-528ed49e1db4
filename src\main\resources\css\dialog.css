/* 邮箱记录对话框样式 */

/* 表格样式 */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.08), 6, 0, 0, 2);
}

/* 表格头部样式 */
.table-view .column-header-background {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 2 0;
}

.table-view .column-header {
    -fx-background-color: transparent;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1 0 0;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
    -fx-padding: 12 8;
}

.table-view .column-header:last-child {
    -fx-border-width: 0;
}

/* 表格行样式 */
.table-row-cell {
    -fx-background-color: white;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0 0 1 0;
    -fx-cell-size: 50px;
}

.table-row-cell:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
}

.table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #2196f3;
}

/* 表格单元格样式 */
.table-cell {
    -fx-padding: 12 16;
    -fx-alignment: center-left;
    -fx-font-size: 13px;
}

/* 分页控件样式 */
.pagination {
    -fx-background-color: transparent;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 15 0 0 0;
}

.pagination .pagination-control {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 4px;
    -fx-padding: 8 12;
    -fx-font-size: 12px;
}

.pagination .pagination-control:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #409EFF;
}

.pagination .pagination-control .current-page {
    -fx-background-color: #409EFF;
    -fx-text-fill: white;
    -fx-border-color: #409EFF;
}

/* 按钮样式增强 */
.button {
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.button:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 4, 0, 0, 2);
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
}

.button:pressed {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 2, 0, 0, 1);
    -fx-scale-y: 0.98;
    -fx-scale-x: 0.98;
}

/* 进度指示器样式 */
.progress-indicator {
    -fx-progress-color: #409EFF;
    -fx-background-color: transparent;
}

.progress-indicator .indicator {
    -fx-background-color: transparent;
}

.progress-indicator .percentage {
    -fx-fill: #409EFF;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

/* 滚动条样式 */
.scroll-bar {
    -fx-background-color: transparent;
}

.scroll-bar .track {
    -fx-background-color: #f8f9fa;
    -fx-border-color: transparent;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.scroll-bar .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #adb5bd;
}

.scroll-bar .thumb:pressed {
    -fx-background-color: #6c757d;
}

/* 标签样式 */
.label {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* 状态标签特殊样式 */
.status-valid {
    -fx-text-fill: #28a745;
    -fx-background-color: #d4edda;
    -fx-background-radius: 4px;
    -fx-padding: 4 8;
    -fx-font-weight: bold;
}

.status-expired {
    -fx-text-fill: #dc3545;
    -fx-background-color: #f8d7da;
    -fx-background-radius: 4px;
    -fx-padding: 4 8;
    -fx-font-weight: bold;
}

.status-unknown {
    -fx-text-fill: #6c757d;
    -fx-background-color: #e2e3e5;
    -fx-background-radius: 4px;
    -fx-padding: 4 8;
}
