package com.ceadeal.javafxboot.ctrl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import com.ceadeal.javafxboot.Application;
import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.util.AppSettings;
import com.ceadeal.javafxboot.util.CursorHelper;
import com.ceadeal.javafxboot.view.LoginView;
import de.felixroske.jfxsupport.FXMLController;
import javafx.animation.FadeTransition;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.TextArea;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;
import javafx.util.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ConfigurableApplicationContext;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.animation.KeyValue;
import javafx.scene.image.ImageView;
import javafx.scene.image.Image;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 *
 * <AUTHOR>
 * @date 2019/4/23 2:01
 */
@Slf4j
@FXMLController
public class MainCtrl implements Initializable {

    @FXML
    private BorderPane rootPane;
    
    @FXML
    private VBox sideMenu;
    
    @FXML
    private Label welcomeLabel;
    
    @FXML
    private Button homeButton;
    
    @FXML
    private Button exchangeButton;
    
    @FXML
    private Button noticeButton;
    
    @FXML
    private Button changePasswordButton;
    
    @FXML
    private Button logoutButton;
    
    @FXML
    private Button settingsButton;
    
    @FXML
    private Pane contentPane;

    // 新增首页UI组件引用
    @FXML
    private Label userNameValue;
    @FXML
    private Label expDateValue;
    @FXML
    private Label emailValue;
    @FXML
    private Label advRequestsValue;
    @FXML
    private ProgressBar advProgressBar;
    @FXML
    private Label noticeLabel;
    @FXML
    private Label welcomeTextLabel;
    @FXML
    private Button switchAccountBtn;
    @FXML
    private Button refreshInfoBtn;
    @FXML
    private Button resetMachineCodeBtn;
    @FXML
    private Label emailCountValue;
    
    // 新增账号级别和剩余时间标签
    private Label accountTypeValue;
    private Label remainingTimeValue;

    // 底部标语标签
    private Label bottomInfoLabel;

    // 售后群图片相关组件
    private StackPane afterSaleGroupPane;
    private ImageView afterSaleGroupImageView;

    // 添加初始化标志，防止重复初始化
    private boolean initialized = false;

    @Autowired
    private ApiService apiService;

    @Autowired
    private SettingsCtrl settingsCtrl;

    // 定时任务相关
    private Timeline refreshTimeline;
    private static final int DEFAULT_REFRESH_INTERVAL = 10; // 默认10分钟刷新一次
    
    // 通知容器和动画
    private HBox noticeBox;
    private Timeline noticeAnimation;
    
    // 添加刷新状态标志，用于防止重复刷新
    private volatile boolean isRefreshing = false;
    // 添加切换代理状态标志，用于防止重复切换
    private volatile boolean isSwitchingAccount = false;
    
    // 公告相关变量
    private Stage noticeStage;
    private boolean publicNoticeShown = false; // 用于跟踪公告是否已显示

    // 缓存管理员权限检测结果
    private Boolean isAdminCache = null;

    /**
     * 暴露给登录控制器使用的方法，在登录后刷新首页信息
     */
    public void refreshInfoAfterLogin() {
        ThreadUtil.execAsync(() -> {
            // 先刷新用户信息
            refreshInfo();

            // 登录成功后，检查并自动启用Max支持
            checkAndAutoEnableMaxSupport();
        });
    }

    /**
     * 异步检查并自动启用Max支持
     */
    private void checkAndAutoEnableMaxSupport() {
        ThreadUtil.execAsync(() -> {
            try {
                log.info("开始检查Max按钮状态...");
                boolean canClickBtn = false;
                try {
                    CommonResult<JSONObject> result = apiService.getBtnControl();
                    if (result != null && result.getIsSuccess() && result.getData() != null) {
                        int flag = result.getData().getInt("canClickBtn", 0);
                        canClickBtn = (flag == 1);
                    }
                } catch (Exception e) {
                    log.error("获取按钮控制信息失败", e);
                }
                if (!canClickBtn) {
                    return;
                }
                // 检查Max按钮是否已启用，不更新UI
                settingsCtrl.checkClaudeMaxStatus(false);

                // 获取检查结果
                boolean isMaxEnabled = settingsCtrl.isClaudeMaxEnabled();
                log.info("Max按钮状态检查完成，当前状态: {}", isMaxEnabled ? "已启用" : "未启用");

                if (!isMaxEnabled) {
                    log.info("Max按钮未启用，开始自动启用...");

                    // 自动启用Max支持，不显示UI提示
                    boolean enableResult = settingsCtrl.enableClaudeMaxSupport(false);

                    if (enableResult) {
                        log.info("Max按钮自动启用成功");
                    } else {
                        log.warn("Max按钮自动启用失败");
                    }
                } else {
                    log.info("Max按钮已经启用，无需处理");
                }

            } catch (Exception e) {
                log.error("检查和自动启用Max支持时发生异常", e);
            }
        });
    }

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Main view initialized");

        if (initialized) {
            log.info("已经初始化过，跳过");
            return;
        }

        initialized = true;

        // 设置欢迎信息
        welcomeLabel.setText("欢迎使用系统！");
        
        // 设置按钮点击事件
        homeButton.setOnAction(this::onHomeButtonClicked);
        exchangeButton.setOnAction(this::onExchangeButtonClicked);
        noticeButton.setOnAction(this::onNoticeButtonClicked);
        changePasswordButton.setOnAction(this::onChangePasswordButtonClicked);
        settingsButton.setOnAction(this::onSettingsButtonClicked);
        logoutButton.setOnAction(this::onLogoutButtonClicked);
        
        // 初始化完成后，获取账号级别和剩余时间标签引用
        Platform.runLater(() -> {
            try {
                accountTypeValue = (Label)lookup("#accountTypeValue");
                remainingTimeValue = (Label)lookup("#remainingTimeValue");
            } catch(Exception e) {
                log.error("获取新增标签引用失败", e);
            }
        });
        
        // 设置按钮样式
        styleActiveButton(homeButton);
        
        // 先在UI线程加载首页内容
        Platform.runLater(() -> {
            showHomeContent(); // 先加载首页内容
            
            // 获取新增标签的引用
            try {
                accountTypeValue = (Label)lookup("#accountTypeValue");
                remainingTimeValue = (Label)lookup("#remainingTimeValue");
                log.info("成功获取新增标签引用: accountTypeValue={}, remainingTimeValue={}", 
                        accountTypeValue != null ? "已找到" : "未找到",
                        remainingTimeValue != null ? "已找到" : "未找到");
            } catch(Exception e) {
                log.error("获取新增标签引用失败", e);
            }
            
            // 立即加载缓存数据（如果有）
            loadCachedUserData();
            
            // 异步刷新数据，避免UI卡顿
            log.info("准备启动异步线程刷新用户数据");
            ThreadUtil.execAsync(() -> {
                try {
                    // 设置合理的重试次数和等待机制
                    int maxRetries = 5;
                    boolean success = false;
                    
                    for (int i = 0; i < maxRetries && !success; i++) {
                        try {
                            log.info("开始刷新用户信息，尝试次数: {}", i + 1);
                            // 先刷新用户信息
                            refreshInfo();
                            success = true;
                            log.info("刷新用户信息完成，准备加载公告");
                        } catch (Exception e) {
                            log.error("刷新用户信息失败，尝试次数: {}, 错误: {}", i + 1, e.getMessage());
                            if (i < maxRetries - 1) {
                                log.info("等待2秒后重试...");
                                ThreadUtil.sleep(1000); // 等待2秒后重试
                            }
                        }
                    }
                    
                    if (success) {
                        // 确保公告在刷新信息后执行，增加延迟以确保稳定显示
                        ThreadUtil.sleep(500);

                        // 加载公告
                        loadPublicNotice();

                        // 异步检查并自动启用Max按钮
                        checkAndAutoEnableMaxSupport();
                    } else {
                        log.error("多次尝试刷新用户信息均失败，不再重试");

                        // 在UI线程中提示用户
                        Platform.runLater(() -> {
                            showToast("加载数据失败，请点击刷新按钮重试", 5000);
                        });
                    }
                } catch (Exception e) {
                    log.error("初始化数据加载出错", e);
                }
            });
        });
    }

    /**
     * 首页按钮点击事件
     */
    private void onHomeButtonClicked(ActionEvent event) {
        resetButtonStyles();
        styleActiveButton(homeButton);
        showHomeContent();
        
        // 从其他菜单切回首页时，重新加载缓存数据
        loadCachedUserData();
    }
    
    /**
     * 显示首页内容
     */
    private void showHomeContent() {
        try {
            log.info("开始加载首页内容");
            // 清除内容面板中的所有内容
            contentPane.getChildren().clear();
            
            // 使用编程方式创建UI组件，避免FXML加载导致重复初始化
            VBox container = new VBox(20);
            container.setPadding(new Insets(30));
            
            // 创建首页内容组件
            log.info("创建首页UI组件");
            createHomeComponents(container);
            
            // 添加到内容面板
            contentPane.getChildren().add(container);
            log.info("首页UI组件创建完成并添加到内容面板");
            
            // 注意：从其他菜单切回首页时，依赖调用方主动调用loadCachedUserData()来加载数据
            log.info("首页加载完成，等待数据更新");
        } catch (Exception e) {
            log.error("加载首页出错", e);
            
            // 显示错误信息
            Label errorLabel = new Label("加载首页数据出错，请稍后再试");
            errorLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #d32f2f;");
            contentPane.getChildren().add(errorLabel);
            
            // 设置标签居中
            errorLabel.layoutXProperty().bind(
                    contentPane.widthProperty().subtract(errorLabel.widthProperty()).divide(2));
            errorLabel.layoutYProperty().bind(
                    contentPane.heightProperty().subtract(errorLabel.heightProperty()).divide(2));
        }
    }
    
    /**
     * 创建首页组件
     */
    private void createHomeComponents(VBox container) {
        // 顶部欢迎区域
        Pane headerPane = new Pane();
        headerPane.setPrefHeight(120);
        headerPane.setStyle("-fx-background-color: linear-gradient(to right, #67C23A, #409EFF); -fx-background-radius: 4;");
        
        welcomeTextLabel = new Label("您好, ");
        welcomeTextLabel.setLayoutX(20);
        welcomeTextLabel.setLayoutY(30);
        welcomeTextLabel.setFont(Font.font(20));
        welcomeTextLabel.setTextFill(Color.WHITE);
        welcomeTextLabel.setStyle("-fx-font-weight: bold;");
        
        noticeLabel = new Label("您的智能编程伙伴已就位，让我们高效构建代码吧！");
        noticeLabel.setLayoutX(20);
        noticeLabel.setLayoutY(70);
        noticeLabel.setFont(Font.font(14));
        noticeLabel.setTextFill(Color.WHITE);
        
        // 添加一个包装noticeLabel的HBox，便于设置样式和动画
        noticeBox = new HBox(noticeLabel);
        noticeBox.setLayoutX(20);
        noticeBox.setLayoutY(70);
        noticeBox.setPadding(new Insets(5, 10, 5, 10));
        
        headerPane.getChildren().addAll(welcomeTextLabel, noticeBox);
        
        // 用户信息和使用统计面板
        GridPane gridPane = new GridPane();
        gridPane.setHgap(20);
        gridPane.setVgap(20);
        gridPane.setPadding(new Insets(15, 0, 0, 0));
        
        // 用户信息区域
        VBox userInfoBox = createUserInfoPanel();
        
        // 使用统计区域
        VBox usageStatBox = createUsageStatPanel();
        
        // 快捷操作区域
        VBox quickActionsBox = createQuickActionsPanel();
        
        // 添加到网格
        gridPane.add(userInfoBox, 0, 0);
        gridPane.add(usageStatBox, 1, 0);
        gridPane.add(quickActionsBox, 0, 1, 2, 1);
        
        // 底部标语区域
        bottomInfoLabel = new Label("当前比较频繁问题会放公告，公告没有的则进官网查找解决方法");
        bottomInfoLabel.setStyle("-fx-text-fill: #333333; -fx-font-size: 13px; -fx-padding: 15 20; -fx-background-color: #f8f8f8; -fx-border-color: #e0e0e0; -fx-border-radius: 4; -fx-background-radius: 4;");
        bottomInfoLabel.setWrapText(true);
        bottomInfoLabel.setMaxWidth(Double.MAX_VALUE);
        VBox bottomBox = new VBox(bottomInfoLabel);
        bottomBox.setPadding(new Insets(10, 0, 0, 0));
        
        // 添加到容器
        container.getChildren().addAll(headerPane, gridPane, bottomBox);
    }

    /**
     * 创建用户信息面板
     */
    private VBox createUserInfoPanel() {
        VBox userInfoBox = new VBox(10);
        userInfoBox.setPadding(new Insets(15));
        userInfoBox.setPrefWidth(400);
        userInfoBox.setStyle("-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);");
        
        Label titleLabel = new Label("用户信息");
        titleLabel.setFont(Font.font(16));
        titleLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #303133;");
        
        GridPane infoGrid = new GridPane();
        infoGrid.setVgap(12);
        infoGrid.setHgap(15);
        infoGrid.setPadding(new Insets(15, 0, 0, 0));
        
        // 用户信息 - 账户名
        Label usernameLabel = new Label("账户名");
        usernameLabel.setStyle("-fx-text-fill: #555555;");
        userNameValue = new Label("");
        userNameValue.setStyle("-fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4; -fx-text-fill: #333333;");
        
        // 过期时间
        Label expDateLabel = new Label("过期时间");
        expDateLabel.setStyle("-fx-text-fill: #555555;");
        expDateValue = new Label("");
        expDateValue.setStyle("-fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4; -fx-text-fill: #333333;");
        
        // 剩余邮箱个数
        Label emailCountLabel = new Label("剩余邮箱");
        emailCountLabel.setStyle("-fx-text-fill: #555555;");
        emailCountValue = new Label("");
        emailCountValue.setStyle("-fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4; -fx-text-fill: #333333;");

        // 邮箱记录按钮（天卡和次卡用户都显示）
        Button emailRecordBtn = new Button("记录");
        emailRecordBtn.setId("emailRecordBtn");
        emailRecordBtn.setPrefHeight(25);
        emailRecordBtn.setPrefWidth(50);
        emailRecordBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 4; -fx-cursor: hand;");
        emailRecordBtn.setOnAction(e -> showEmailRecordDialog());
        emailRecordBtn.setVisible(true); // 默认显示

        // 创建剩余邮箱的容器，包含标签和按钮
        HBox emailCountContainer = new HBox(5);
        emailCountContainer.getChildren().addAll(emailCountValue, emailRecordBtn);
        emailCountContainer.setAlignment(Pos.CENTER_LEFT);

        // 添加到网格
        infoGrid.add(usernameLabel, 0, 0);
        infoGrid.add(userNameValue, 1, 0);
        infoGrid.add(expDateLabel, 0, 1);
        infoGrid.add(expDateValue, 1, 1);
        infoGrid.add(emailCountLabel, 0, 2);
        infoGrid.add(emailCountContainer, 1, 2);
        
        userInfoBox.getChildren().addAll(titleLabel, infoGrid);
        return userInfoBox;
    }

    /**
     * 创建使用统计面板
     */
    private VBox createUsageStatPanel() {
        VBox usageStatBox = new VBox(10); // 减小间距
        usageStatBox.setPadding(new Insets(15));
        usageStatBox.setPrefWidth(400);
        usageStatBox.setStyle("-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);");
        
        Label titleLabel = new Label("使用统计");
        titleLabel.setFont(Font.font(16));
        titleLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #303133;");
        
        // 移除当前邮箱部分，减少布局高度
        
        // 创建一个包含邮箱、账号级别和剩余时间的网格布局
        GridPane accountInfoGrid = new GridPane();
        accountInfoGrid.setHgap(15); // 水平间隙
        accountInfoGrid.setVgap(10); // 垂直间隙增大一些
        
        // 邮箱信息
        Label emailLabel = new Label("当前邮箱");
        emailLabel.setStyle("-fx-text-fill: #555555;");
        
        emailValue = new Label("");
        emailValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #333333; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
        emailValue.setPrefWidth(250); // 设置适当的宽度
        
        // 账号级别 - 放在左侧
        Label accountTypeLabel = new Label("账号级别");
        accountTypeLabel.setStyle("-fx-text-fill: #555555;");
        
        Label accountTypeValue = new Label("");
        accountTypeValue.setId("accountTypeValue");
        accountTypeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #333333; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
        // 根据列约束自适应大小
        
        // 状态 - 放在右侧 (替换原来的剩余时间)
        Label remainingTimeLabel = new Label("状态");
        remainingTimeLabel.setStyle("-fx-text-fill: #555555;");
        
        Label remainingTimeValue = new Label("");
        remainingTimeValue.setId("remainingTimeValue");
        remainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #333333; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
        // 根据列约束自适应大小
        
        // 添加到网格 - 邮箱标签和值在同一行
        accountInfoGrid.add(emailLabel, 0, 0);
        accountInfoGrid.add(emailValue, 1, 0);
        
        // 账号级别和剩余时间信息
        accountInfoGrid.add(accountTypeLabel, 0, 1);
        accountInfoGrid.add(remainingTimeLabel, 1, 1);
        accountInfoGrid.add(accountTypeValue, 0, 2);
        accountInfoGrid.add(remainingTimeValue, 1, 2);
        
        // 移除高级模型用量显示
        
        // 添加所有组件到主容器
        usageStatBox.getChildren().addAll(titleLabel, accountInfoGrid);
        return usageStatBox;
    }

    /**
     * 创建快捷操作面板
     */
    private VBox createQuickActionsPanel() {
        VBox quickActionsBox = new VBox(15);
        quickActionsBox.setPadding(new Insets(15));
        quickActionsBox.setStyle("-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);");
        
        // 创建标题和问号按钮的容器
        HBox titleContainer = new HBox(10);
        titleContainer.setAlignment(Pos.CENTER_LEFT);
        
        Label titleLabel = new Label("快捷操作");
        titleLabel.setFont(Font.font(16));
        titleLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #303133;");
        
        // 创建问号按钮
        Button helpButton = new Button("?");
        helpButton.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 50%; -fx-min-width: 24; -fx-min-height: 24; -fx-max-width: 24; -fx-max-height: 24; -fx-cursor: hand;");
        
        // 检查是否以管理员权限运行
        boolean isAdmin = isRunAsAdministrator();
        
        // 添加问号按钮点击事件
        helpButton.setOnAction(event -> {
            Alert helpAlert = new Alert(Alert.AlertType.INFORMATION);
            helpAlert.setTitle("按钮说明");
            helpAlert.setHeaderText("快捷操作按钮说明");
            
            // 按钮说明内容
            String helpContent = "切换代理/账户异常专用：次卡（每次点击求切换新的邮箱，正常使用最少3天有效期）,天卡（避免滥用需使用一定次数或邮箱失效后才会分配新邮箱）\n\n" +
                    "重置机器码：无需每次重置，还不行手动执行exe下命令直接运行，提示当前机器免费账号使用过多时需重置\n\n" +
                    "账户异常专用：当官方会锁邮箱的时候才需用，后台不设置的情况下该按钮与切换代理按钮一致\n\n" +
                    "访问官网：使用及常见问题对应解决方案\n\n"+
                    "邮箱账号级别为free或者过期时需要重新切换邮箱+机器码，否则只能auto";

            
            helpAlert.setContentText(helpContent);
            helpAlert.showAndWait();
        });
        
        // 创建管理员权限提示（只在非管理员运行时显示）
        Label adminWarningLabel = null;
        if (!isAdmin) {
            adminWarningLabel = new Label("非管理员运行软件，请以管理员权限运行软件，以减少使用问题");
            adminWarningLabel.setStyle("-fx-text-fill: #F56C6C; -fx-font-size: 12px;");
            HBox.setMargin(adminWarningLabel, new Insets(4, 0, 0, 10));
        }
        
        // 添加组件到标题容器
        if (adminWarningLabel != null) {
            titleContainer.getChildren().addAll(titleLabel, helpButton, adminWarningLabel);
        } else {
            titleContainer.getChildren().addAll(titleLabel, helpButton);
        }
        
        GridPane actionsGrid = new GridPane();
        actionsGrid.setHgap(20);
        actionsGrid.setVgap(20);
        actionsGrid.setPadding(new Insets(15, 0, 0, 0));
        
        // 创建操作按钮
        switchAccountBtn = new Button("切换代理账号（账户+机器码）");
        switchAccountBtn.setPrefHeight(40);
        switchAccountBtn.setPrefWidth(220);
        switchAccountBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        // 新增切换代理账号按钮（无需重置机器码）
        Button switchAccountNoResetBtn = new Button("切换代理账号");
        switchAccountNoResetBtn.setId("switchAccountNoResetBtn"); // 添加ID便于查找
        switchAccountNoResetBtn.setPrefHeight(40);
        switchAccountNoResetBtn.setPrefWidth(220);
        switchAccountNoResetBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        refreshInfoBtn = new Button("刷新信息");
        refreshInfoBtn.setPrefHeight(40);
        refreshInfoBtn.setPrefWidth(220);
        refreshInfoBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        // 新增IP异常切换按钮
        Button forceRefreshBtn = new Button("账户异常专用");
        forceRefreshBtn.setPrefHeight(40);
        forceRefreshBtn.setPrefWidth(220);
        forceRefreshBtn.setStyle("-fx-background-color: #F56C6C; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        resetMachineCodeBtn = new Button("重置机器码");
        resetMachineCodeBtn.setPrefHeight(40);
        resetMachineCodeBtn.setPrefWidth(220);
        resetMachineCodeBtn.setStyle("-fx-background-color: #E6A23C; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        // 新增官网按钮
        Button officialWebsiteBtn = new Button("官网(常见问题)");
        officialWebsiteBtn.setPrefHeight(40);
        officialWebsiteBtn.setPrefWidth(220);
        officialWebsiteBtn.setStyle("-fx-background-color: #67C23A; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
        
        // 设置按钮事件和样式
        switchAccountBtn.setOnAction(event -> {
            if (!isAdmin) {
                Platform.runLater(() -> showAlert("权限不足", "此功能需要管理员权限运行软件，请以管理员身份重新启动软件"));
                return;
            }
            ThreadUtil.execAsync(() -> switchAccount(0, true));
        });
        switchAccountNoResetBtn.setOnAction(event -> ThreadUtil.execAsync(() -> switchAccount(0, false)));
        refreshInfoBtn.setOnAction(event -> onRefreshInfoBtnClicked());
        forceRefreshBtn.setOnAction(event -> {
            if (!isAdmin) {
                Platform.runLater(() -> showAlert("权限不足", "此功能需要管理员权限运行软件，请以管理员身份重新启动软件"));
                return;
            }
            ThreadUtil.execAsync(() -> switchAccount(1, true));
        });
        resetMachineCodeBtn.setOnAction(event -> {
            if (!isAdmin) {
                Platform.runLater(() -> showAlert("权限不足", "此功能需要管理员权限运行软件，请以管理员身份重新启动软件"));
                return;
            }
            onResetMachineCodeBtnClicked();
        });
        officialWebsiteBtn.setOnAction(event -> onOfficialWebsiteBtnClicked());
        
        // 设置鼠标悬停效果
        switchAccountBtn.setOnMouseEntered(e -> switchAccountBtn.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        switchAccountBtn.setOnMouseExited(e -> switchAccountBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        
        switchAccountNoResetBtn.setOnMouseEntered(e -> switchAccountNoResetBtn.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        switchAccountNoResetBtn.setOnMouseExited(e -> switchAccountNoResetBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        
        refreshInfoBtn.setOnMouseEntered(e -> refreshInfoBtn.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        refreshInfoBtn.setOnMouseExited(e -> refreshInfoBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        
        forceRefreshBtn.setOnMouseEntered(e -> forceRefreshBtn.setStyle("-fx-background-color: #f78989; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        forceRefreshBtn.setOnMouseExited(e -> forceRefreshBtn.setStyle("-fx-background-color: #F56C6C; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        
        resetMachineCodeBtn.setOnMouseEntered(e -> resetMachineCodeBtn.setStyle("-fx-background-color: #edc180; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        resetMachineCodeBtn.setOnMouseExited(e -> resetMachineCodeBtn.setStyle("-fx-background-color: #E6A23C; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
        
        // 官网按钮悬停效果，添加售后群图片显示功能
        officialWebsiteBtn.setOnMouseEntered(e -> {
            officialWebsiteBtn.setStyle("-fx-background-color: #85ce61; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
            // 显示二维码图片
            String imgUrl = apiService.getHomeData().getOrDefault("afterSaleGroupImg", "").toString();
            if (StrUtil.isNotBlank(imgUrl) && afterSaleGroupPane != null) {
                // 定位售后群图片位置
                afterSaleGroupPane.setLayoutX(officialWebsiteBtn.getLayoutX() + officialWebsiteBtn.getWidth() / 2);
                afterSaleGroupPane.setLayoutY(officialWebsiteBtn.getLayoutY() - 150);
                afterSaleGroupPane.setVisible(true);
                
                // 淡入效果
                FadeTransition fadeIn = new FadeTransition(Duration.millis(200), afterSaleGroupPane);
                fadeIn.setFromValue(0);
                fadeIn.setToValue(1);
                fadeIn.play();
            }
        });
        
        officialWebsiteBtn.setOnMouseExited(e -> {
            officialWebsiteBtn.setStyle("-fx-background-color: #67C23A; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
            // 隐藏二维码图片
            if (afterSaleGroupPane != null) {
                // 淡出效果
                FadeTransition fadeOut = new FadeTransition(Duration.millis(200), afterSaleGroupPane);
                fadeOut.setFromValue(1);
                fadeOut.setToValue(0);
                fadeOut.setOnFinished(event -> afterSaleGroupPane.setVisible(false));
                fadeOut.play();
            }
        });
        
        // 按照需求调整按钮布局
        actionsGrid.add(switchAccountBtn, 0, 0);
        actionsGrid.add(switchAccountNoResetBtn, 1, 0);
        actionsGrid.add(refreshInfoBtn, 2, 0);
        actionsGrid.add(resetMachineCodeBtn, 0, 1);
        actionsGrid.add(forceRefreshBtn, 1, 1);
        actionsGrid.add(officialWebsiteBtn, 2, 1);
        
        // 创建售后群图片组件
        createAfterSaleGroupComponent(actionsGrid);
        
        quickActionsBox.getChildren().addAll(titleContainer, actionsGrid);
        return quickActionsBox;
    }
    
    /**
     * 创建售后群图片组件
     */
    private void createAfterSaleGroupComponent(GridPane actionsGrid) {
        // 创建售后群图片组件
        afterSaleGroupImageView = new ImageView();
        afterSaleGroupImageView.setFitWidth(200);
        afterSaleGroupImageView.setFitHeight(200);
        afterSaleGroupImageView.setPreserveRatio(true);
        
        // 创建包含图片的容器
        afterSaleGroupPane = new StackPane(afterSaleGroupImageView);
        afterSaleGroupPane.setStyle("-fx-background-color: white; -fx-padding: 10; -fx-background-radius: 5; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 10, 0, 0, 10);");
        afterSaleGroupPane.setVisible(false);
        afterSaleGroupPane.setManaged(false);
        
        // 图片面板鼠标事件响应
        afterSaleGroupPane.setOnMouseEntered(e -> {
            if (afterSaleGroupPane != null) {
                afterSaleGroupPane.setVisible(true);
            }
        });
        
        afterSaleGroupPane.setOnMouseExited(e -> {
            if (afterSaleGroupPane != null) {
                // 淡出效果
                FadeTransition fadeOut = new FadeTransition(Duration.millis(200), afterSaleGroupPane);
                fadeOut.setFromValue(1);
                fadeOut.setToValue(0);
                fadeOut.setOnFinished(event -> afterSaleGroupPane.setVisible(false));
                fadeOut.play();
            }
        });
        
        // 防止容器被网格布局管理
        StackPane.setAlignment(afterSaleGroupImageView, Pos.CENTER);
        
        // 添加图片容器到网格的父容器
        Pane parent = (Pane) actionsGrid.getParent();
        if (parent != null) {
            parent.getChildren().add(afterSaleGroupPane);
        } else {
            // 如果找不到父容器，则保存引用，稍后添加
            Platform.runLater(() -> {
                Pane newParent = (Pane) actionsGrid.getParent();
                if (newParent != null && !newParent.getChildren().contains(afterSaleGroupPane)) {
                    newParent.getChildren().add(afterSaleGroupPane);
                }
            });
        }
    }
    
    /**
     * 更新售后群图片
     */
    private void updateAfterSaleGroupImage(String imgUrl) {
        if (StrUtil.isNotBlank(imgUrl) && afterSaleGroupImageView != null) {
            try {
                Image image = new Image(imgUrl);
                afterSaleGroupImageView.setImage(image);
                afterSaleGroupImageView.setFitWidth(250);
                afterSaleGroupImageView.setFitHeight(350);
                afterSaleGroupImageView.setPreserveRatio(false);

                log.info("图片加载: 宽度={}, 高度={}", image.getWidth(), image.getHeight());
            } catch (Exception e) {
                log.error("加载售后群图片失败: {}", imgUrl, e);
            }
        }
    }

    /**
     * 刷新信息按钮点击事件
     */
    private void onRefreshInfoBtnClicked() {
        // 检查是否已经在刷新中
        if(isRefreshing) {
            showToast("正在刷新中，请稍候...", 2000);
            return;
        }
        
        // 显示加载提示
        showToast("正在刷新数据...", 2000);
        
        // 使用后台线程刷新数据，避免UI卡顿
        ThreadUtil.execAsync(this::refreshInfo);
    }

    private void refreshInfo() {
        // 如果已经在刷新，则直接返回
        if(!isRefreshing) {
            isRefreshing = true;  // 设置刷新标志
            log.info("开始刷新用户信息");
            try {
                showToast("正在刷新信息", 4000);
                // 异步获取邮箱和使用限制信息
                log.info("开始获取邮箱和使用限制信息");
                refreshEmailAndLimit(false);
                log.info("完成邮箱和使用限制信息刷新");
                
                // 获取用户信息
                log.info("开始获取用户基本信息");
                CommonResult<JSONObject> result = apiService.getUserInfo();
                log.info("getUserInfo API调用返回结果: isSuccess={}", result.getIsSuccess());
                if (!result.getIsSuccess()) {
                    log.error("获取用户信息失败: {}", result.getMsg());
                    Platform.runLater(() -> showAlert("获取用户信息错误", result.getMsg()));
                    return;
                }
                log.info("成功获取用户信息");

                // 提取数据
                String username = result.getData().getOrDefault("username", "").toString();
                String cursorExpdate = result.getData().getOrDefault("cursorExpdate", "").toString();
                String notice = result.getData().getOrDefault("notice", "").toString();
                String gwurl = result.getData().getOrDefault("gwurl", "").toString();
                String afterSaleGroupImg = result.getData().getOrDefault("afterSaleGroupImg", "").toString();
                String cursorEmailCount = result.getData().getOrDefault("cursorEmailCount", "").toString();
                String noticeHighlightFlag = result.getData().getOrDefault("noticeHighlightFlag", "0").toString();
                String dibuinfo = result.getData().getOrDefault("dibuinfo", "").toString();
                String userType = result.getData().getOrDefault("userType", "1").toString(); // 1-天卡，2-次卡
                String canClickQhdl = result.getData().getOrDefault("canClickQhdl", "1").toString(); // 是否可以点击切换代理按钮
                
                log.info("获取用户信息成功: 用户名={}, 过期时间={}", username, cursorExpdate);
                
                // 更新ApiService中的homeData
                apiService.updateHomeData("username", username);
                apiService.updateHomeData("cursorExpdate", cursorExpdate);
                apiService.updateHomeData("notice", notice);
                apiService.updateHomeData("gwurl", gwurl);
                apiService.updateHomeData("afterSaleGroupImg", afterSaleGroupImg);
                apiService.updateHomeData("cursorEmailCount", cursorEmailCount);
                apiService.updateHomeData("noticeHighlightFlag", noticeHighlightFlag);
                apiService.updateHomeData("dibuinfo", dibuinfo);
                apiService.updateHomeData("userType", userType);
                apiService.updateHomeData("canClickQhdl", canClickQhdl);
                
                // 获取邮箱相关信息（已在refreshEmailAndLimit中加载）
                String email = apiService.getHomeData().getOrDefault("email", "").toString();
                String numRequests = apiService.getHomeData().getOrDefault("numRequests", "0").toString();
                String maxRequestUsage = apiService.getHomeData().getOrDefault("maxRequestUsage", "0").toString();
                
                log.info("准备更新UI界面信息");
                
                // 在UI线程更新UI组件
                Platform.runLater(() -> {
                    try {
                        // 使用统一的方法更新UI组件
                        updateUIWithUserData(username, cursorExpdate, email, numRequests, maxRequestUsage,
                                           notice, afterSaleGroupImg, cursorEmailCount, noticeHighlightFlag, userType, canClickQhdl);
                        
                        // 更新完成后显示提示
                        showToast("信息刷新成功", 4000);
                        log.info("UI更新成功，显示刷新成功提示");
                    } catch (Exception e) {
                        log.error("更新UI组件出错", e);
                        showAlert("刷新信息错误", "更新UI组件时发生错误");
                    }
                });

                log.info("信息刷新成功：用户[{}]，过期时间[{}]", username, cursorExpdate);
            } catch (Exception e) {
                log.error("刷新信息出错", e);
                Platform.runLater(() -> showAlert("刷新信息错误", "获取数据时发生错误，请稍后再试"));
            } finally {
                // 无论刷新成功还是失败，都重置刷新标志
                isRefreshing = false;
            }
        } else {
            // 已经有刷新正在进行中，忽略本次请求
            Platform.runLater(() -> showToast("正在刷新中，请稍候...", 2000));
        }
    }

    private String refreshEmailAndLimit(boolean flag) {
        try {
            // 查看当前邮箱信息
            log.debug("开始获取邮箱信息");
            AppSettings appSettings = apiService.getAppSettings();
            Map<String, Object> readAuthInfo = CursorHelper.readAuthInfo(appSettings.getVscdbPath());
            
            if (readAuthInfo == null || readAuthInfo.isEmpty()) {
                log.error("无法读取Cursor认证信息");
                return null;
            }
            
            String email = readAuthInfo.getOrDefault("cursorAuth/cachedEmail", "").toString();
            String accessToken = readAuthInfo.getOrDefault("cursorAuth/accessToken", "").toString();
            
            log.debug("获取到邮箱: {}", email);
            
            // 添加邮箱到ApiService的数据中
            apiService.updateHomeData("email", email);
            
            // 在UI线程更新基本邮箱信息
            Platform.runLater(() -> {
                try {
                                    // 更新邮箱显示
                if (emailValue != null) emailValue.setText(email);
                    
                    // 设置高级模型用量为无限制
                    if (advRequestsValue != null) {
                        advRequestsValue.setText("∞/∞");
                    }
                    
                    // 设置进度条为满进度绿色
                    if (advProgressBar != null) {
                        advProgressBar.setProgress(1.0);
                    }
                } catch (Exception e) {
                    log.error("更新UI组件出错", e);
                }
            });
            
            // 获取账号信息和剩余时间
            if (accessToken != null && !accessToken.isEmpty()) {
                try {
                    JSONObject emailInfo = getEmailInfo(accessToken);
                    if (emailInfo != null) {
                        String membershipType = emailInfo.getStr("membershipType");
                        String daysRemainingOnTrial = emailInfo.getStr("daysRemainingOnTrial");

                        // 格式化账号级别显示
                        String displayMembershipType = "free_trial".equals(membershipType) ? "Pro Trial" : membershipType;

                        // 保存到ApiService的数据中
                        apiService.updateHomeData("membershipType", displayMembershipType);
                        apiService.updateHomeData("daysRemainingOnTrial", daysRemainingOnTrial);

                        // 更新UI
                        Platform.runLater(() -> {
                            try {
                                // 重新查找UI组件以确保引用有效
                                Label currentAccountTypeValue = (Label)lookup("#accountTypeValue");
                                Label currentRemainingTimeValue = (Label)lookup("#remainingTimeValue");

                                // 更新账号级别
                                if (currentAccountTypeValue != null) {
                                    currentAccountTypeValue.setText(displayMembershipType);
                                    log.debug("成功更新账号级别UI: {}", displayMembershipType);
                                } else {
                                    log.warn("无法找到accountTypeValue组件，账号级别更新失败");
                                }

                                // 更新状态(根据剩余天数判断)
                                if (currentRemainingTimeValue != null && NumberUtil.isNumber(daysRemainingOnTrial)) {
                                    int days = Integer.parseInt(daysRemainingOnTrial);
                                    if (days > 0) {
                                        currentRemainingTimeValue.setText("有效");
                                        currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #67C23A; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                                    } else {
                                        currentRemainingTimeValue.setText("过期");
                                        currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #F56C6C; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                                    }
                                    log.debug("成功更新状态UI: 剩余天数={}, 状态={}", daysRemainingOnTrial, days > 0 ? "有效" : "过期");
                                } else if (currentRemainingTimeValue != null) {
                                    currentRemainingTimeValue.setText("");
                                    log.debug("剩余天数无效，清空状态显示");
                                }

                            } catch (Exception e) {
                                log.error("更新账号信息UI组件出错", e);
                            }
                        });

                        log.info("账号信息刷新成功：账号级别[{}]，剩余天数[{}]",
                               displayMembershipType, daysRemainingOnTrial);
                    } else {
                        log.warn("获取账号信息返回null，可能是API调用失败或网络问题");
                        // 当获取账号信息失败时，设置默认值避免显示空白
                        Platform.runLater(() -> {
                            try {
                                Label currentAccountTypeValue = (Label)lookup("#accountTypeValue");
                                Label currentRemainingTimeValue = (Label)lookup("#remainingTimeValue");

                                if (currentAccountTypeValue != null) {
                                    currentAccountTypeValue.setText("获取失败");
                                    log.debug("设置账号级别为获取失败状态");
                                }
                                if (currentRemainingTimeValue != null) {
                                    currentRemainingTimeValue.setText("未知");
                                    currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #909399; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                                    log.debug("设置状态为未知状态");
                                }
                            } catch (Exception e) {
                                log.error("设置默认账号信息UI出错", e);
                            }
                        });
                    }
                } catch (Exception e) {
                    log.error("获取账号信息出错", e);
                    // 当发生异常时，也设置默认值
                    Platform.runLater(() -> {
                        try {
                            Label currentAccountTypeValue = (Label)lookup("#accountTypeValue");
                            Label currentRemainingTimeValue = (Label)lookup("#remainingTimeValue");

                            if (currentAccountTypeValue != null) {
                                currentAccountTypeValue.setText("获取异常");
                            }
                            if (currentRemainingTimeValue != null) {
                                currentRemainingTimeValue.setText("异常");
                                currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #F56C6C; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                            }
                        } catch (Exception uiEx) {
                            log.error("设置异常状态UI出错", uiEx);
                        }
                    });
                }
            } else {
                log.warn("accessToken为空，无法获取账号信息");
                // 当accessToken为空时，设置相应提示
                Platform.runLater(() -> {
                    try {
                        Label currentAccountTypeValue = (Label)lookup("#accountTypeValue");
                        Label currentRemainingTimeValue = (Label)lookup("#remainingTimeValue");

                        if (currentAccountTypeValue != null) {
                            currentAccountTypeValue.setText("未登录");
                        }
                        if (currentRemainingTimeValue != null) {
                            currentRemainingTimeValue.setText("未登录");
                            currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #909399; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                        }
                    } catch (Exception e) {
                        log.error("设置未登录状态UI出错", e);
                    }
                });
            }
            
            return "unlimited"; // 返回无限制标志
        } catch (Exception e) {
            log.error("刷新邮箱信息出错: {}", e.getMessage(), e);
            return null;
        }
    }

    private void switchAccount(int forceFlag, boolean resetMachineFlag) {
        try {
            // 检查是否已经在切换代理中
            if(isSwitchingAccount) {
                showToast("正在切换代理中，请稍候...", 3000);
                return;
            }

            // 设置切换中标识
            isSwitchingAccount = true;

            // 显示吐司提示
            showToast("正在切换代理账号，请稍候...", 4000);

            // 用户自行实现具体逻辑
            CommonResult<JSONObject> result = apiService.refreshEmail(forceFlag);
            if (result.getIsSuccess()) {
                log.info("返回代理信息成功: {}", result.getData().getStr("email").replace("2925.com", "outlook.com"));

                // 检查返回数据中的nonewFlag，只有不为1时才执行DNS刷新
                JSONObject data = result.getData();
                if (!"1".equals(data.getStr("nonewFlag"))) {
                    log.info("检测到nonewFlag=1，开始刷新DNS缓存...");
                    try {
                        CursorHelper.executeWindowsCommand("ipconfig/flushdns");
                        log.info("刷新DNS缓存成功");
                    } catch (Exception e) {
                        log.error("刷新DNS缓存失败", e);
                        showToast("刷新DNS缓存失败，但代理切换继续进行...", 2000);
                    }
                } else {
                    log.info("nonewFlag不为1，跳过DNS刷新操作");
                }
                // 关闭cursor应用
                Platform.runLater(() -> showToast("正在关闭Cursor应用...", 3000));
                try {
                    CursorHelper.executeWindowsCommand("taskkill /f /im Cursor.exe");
                    log.info("关闭Cursor应用成功");
                } catch (Exception e) {
                    log.error("关闭Cursor应用失败", e);
                    // 关闭失败不影响后续操作
                }
                //更新cursortoken
                String cursorToken = result.getData().get("token").toString();
                String accessToken = result.getData().get("accessToken").toString();
                String refreshToken = result.getData().get("refreshToken").toString();
                String email = result.getData().get("email").toString().replace("2925.com", "outlook.com");
                boolean flag = CursorHelper.updateAuthInfo(email, accessToken, refreshToken, apiService.getAppSettings().getVscdbPath());

                if (!flag) {
                    Platform.runLater(() -> showAlert("切换代理错误", "cursor应用数据路径错误，按公告里方法查找正确的路径"));
                    return;
                }

                // 异步刷新邮箱信息，但不触发新的switchAccount (传false)
                ThreadUtil.execAsync(() -> refreshInfo());
//                if (forceFlag == 1) {
                    log.info("刷新cursor缓存中...");
                    try {
                        if (result.getData().get("delCursorPaths") != null) {
                            String delCursorPaths = result.getData().get("delCursorPaths").toString();
                            if (!delCursorPaths.equals("{}")) {
                                List<String> paths = StrUtil.split(delCursorPaths, ",");
                                paths.forEach(path -> {
                                    try {
                                        String delPath = apiService.getAppSettings().getVscdbPath() + path;
                                        FileUtil.del(delPath);
    //                                    log.info("删除cursor缓存成功" + delPath);
                                    } catch (IORuntimeException e) {
                                        log.error("刷新cursor缓存失败");
                                    }
                                });
                            }
                        }

                    } catch (Exception e) {
                        log.error("刷新cursor缓存失败");
                    }
//                }
                // 根据标志决定是否重置机器码
                if (resetMachineFlag) {
                    log.info("切换代理成功，重置机器码中...");
                    //重置机器码
                    flag = CursorHelper.resetMachineCode(apiService.getAppSettings().getVscdbPath(), apiService.getAppSettings().getCursorPath());
                    if (!flag) {
                        Platform.runLater(() -> showAlert("重置机器码错误", "代理切换成功,机器码重置失败(管理员运行软件多尝试几次或手动执行exe下命令）"));
                        openCursor();
                        return;
                    }
                }
                openCursor();
                Platform.runLater(() -> showAlert("切换代理成功", "切换代理成功"));
            } else {
                log.error("切换代理失败: {}", result.getMsg());
                Platform.runLater(() -> showAlert("切换代理错误", result.getMsg()));
            }
        } catch (Exception e) {
            log.error("切换代理异常", e);
            Platform.runLater(() -> showAlert("切换代理异常", e.getMessage()));
        } finally {
            // 无论成功还是失败，都重置切换标识
            isSwitchingAccount = false;
        }
    }

    private void openCursor() {
        // 使用SettingsCtrl中的静态方法打开Cursor应用
        Platform.runLater(() -> showToast("正在启动Cursor应用...", 3000));
        SettingsCtrl.openCursor(apiService.getAppSettings().getCursorPath());
    }

    /**
     * 显示吐司提示
     * @param message 提示消息
     * @param duration 显示时长(毫秒)
     */
    private void showToast(String message, int duration) {
        Platform.runLater(() -> {
            try {
                // 创建吐司提示标签
                Label toastLabel = new Label(message);
                toastLabel.setStyle("-fx-background-color: rgba(50, 50, 50, 0.8); -fx-text-fill: white; -fx-padding: 10 20; -fx-background-radius: 20; -fx-font-size: 14px;");

                // 使用VBox容器并设置底部对齐
                VBox toastBox = new VBox(toastLabel);
                toastBox.setAlignment(Pos.CENTER);
                toastBox.setStyle("-fx-padding: 20;");

                // 创建覆盖层
                AnchorPane overlay = new AnchorPane(toastBox);
                overlay.setPrefSize(contentPane.getWidth(), contentPane.getHeight());
                overlay.setPickOnBounds(false); // 允许点击穿透

                // 将吐司放在底部中间
                AnchorPane.setBottomAnchor(toastBox, 50.0);
                AnchorPane.setLeftAnchor(toastBox, 0.0);
                AnchorPane.setRightAnchor(toastBox, 0.0);

                // 初始设置为透明
                overlay.setOpacity(0);

                // 添加到内容面板
                contentPane.getChildren().add(overlay);

                // 淡入动画
                FadeTransition fadeIn = new FadeTransition(Duration.millis(300), overlay);
                fadeIn.setFromValue(0);
                fadeIn.setToValue(1);

                // 淡出动画
                FadeTransition fadeOut = new FadeTransition(Duration.millis(500), overlay);
                fadeOut.setFromValue(1);
                fadeOut.setToValue(0);
                fadeOut.setDelay(Duration.millis(duration));
                fadeOut.setOnFinished(e -> contentPane.getChildren().remove(overlay));

                // 播放动画
                fadeIn.play();
                fadeOut.play();
            } catch (Exception e) {
                log.error("显示吐司提示失败", e);
            }
        });
    }

    /**
     * 激活码兑换按钮点击事件
     */
    private void onExchangeButtonClicked(ActionEvent event) {
        resetButtonStyles();
        styleActiveButton(exchangeButton);
        
        try {
            // 加载激活码兑换页面内容
            loadContentView("/fxml/Exchange.fxml");
        } catch (Exception e) {
            log.error("加载激活码兑换页面出错", e);
            showAlert("错误", "加载激活码兑换页面失败！");
        }
    }

    /**
     * 公告信息按钮点击事件
     */
    private void onNoticeButtonClicked(ActionEvent event) {
        resetButtonStyles();
        styleActiveButton(noticeButton);
        
        try {
            // 加载公告信息页面内容
            loadContentView("/fxml/Notice.fxml");
        } catch (Exception e) {
            log.error("加载公告信息页面出错", e);
            showAlert("错误", "加载公告信息页面失败！");
        }
    }

    /**
     * 修改密码按钮点击事件
     */
    private void onChangePasswordButtonClicked(ActionEvent event) {
        resetButtonStyles();
        styleActiveButton(changePasswordButton);
        
        try {
            // 加载修改密码页面内容
            loadContentView("/fxml/ChangePassword.fxml");
        } catch (Exception e) {
            log.error("加载修改密码页面出错", e);
            showAlert("错误", "加载修改密码页面失败！");
        }
    }
    
    /**
     * 加载内容到内容面板
     */
    private void loadContentView(String fxmlPath) throws IOException {
        // 清除内容面板中的内容
        contentPane.getChildren().clear();
        
        ConfigurableApplicationContext ctx = Application.getApplicationContext();
        
        // 加载FXML文件
        FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
        
        // 设置控制器工厂，使用Spring创建控制器
        loader.setControllerFactory(ctx::getBean);
        
        // 加载视图
        Parent root = loader.load();
        
        // 调整加载的视图大小以适应contentPane
        root.prefWidth(contentPane.getWidth());
        root.prefHeight(contentPane.getHeight());
        
        // 添加到内容面板
        contentPane.getChildren().add(root);
    }
    
    /**
     * 设置按钮为激活状态样式
     */
    private void styleActiveButton(Button button) {
        button.setStyle("-fx-background-color: #4e5254; -fx-text-fill: white;");
    }
    
    /**
     * 重置所有按钮样式
     */
    private void resetButtonStyles() {
        homeButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white;");
        exchangeButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white;");
        noticeButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white;");
        changePasswordButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white;");
        settingsButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white;");
    }

    /**
     * 退出按钮点击事件
     */
    private void onLogoutButtonClicked(ActionEvent event) {
        // 禁用退出按钮防止重复点击
        logoutButton.setDisable(true);

        // 使用新线程执行登出操作，防止UI阻塞
        new Thread(() -> {
            // 调用登出接口
            boolean logoutSuccess = apiService.logout();
            logoutButton.setDisable(false);
            // 回到UI线程处理结果
            Platform.runLater(() -> {
                if (logoutSuccess) {
                    // 清除token
                    apiService.getAppSettings().updateToken(null);
                    
                    // 返回到登录页面前先清理资源
                    try {
                        // 可能需要取消定时任务或其他资源
                        if (refreshTimeline != null) {
                            refreshTimeline.stop();
                            refreshTimeline = null;
                        }

                        // 返回到登录页面
                        Application.showView(LoginView.class);
                    } catch (Exception e) {
                        log.error("切换到登录视图失败", e);
                        // 恢复退出按钮状态
                        logoutButton.setDisable(false);
                    }
                } else {
                    showAlert("登出失败", "请稍后再试！");
                    // 恢复退出按钮状态
                    logoutButton.setDisable(false);
                }
            });
        }).start();
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }

    /**
     * 设置按钮点击事件
     */
    private void onSettingsButtonClicked(ActionEvent event) {
        resetButtonStyles();
        styleActiveButton(settingsButton);
        
        try {
            // 加载设置页面内容
            loadContentView("/fxml/Settings.fxml");
        } catch (Exception e) {
            log.error("加载设置页面出错", e);
            showAlert("错误", "加载设置页面失败！");
        }
    }

    /**
     * 重置机器码按钮点击事件
     */
    private void onResetMachineCodeBtnClicked() {
        // 显示确认对话框
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认操作");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("确定要重置机器码吗？此操作将修改系统标识。");
        
        // 等待用户确认
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == javafx.scene.control.ButtonType.OK) {
                // 显示加载提示
                showToast("正在重置机器码，请稍候...", 3000);
                
                // 使用后台线程执行重置操作
                ThreadUtil.execAsync(() -> {
                    boolean success = CursorHelper.resetMachineCode(apiService.getAppSettings().getVscdbPath(), apiService.getAppSettings().getCursorPath());
                    
                    // 在UI线程更新结果
                    Platform.runLater(() -> {
                        if (success) {
                            showAlert("操作成功", "机器码重置成功！");
                            openCursor();
                        } else {
                            showAlert("操作失败", "机器码重置失败，请以管理员权限运行软件或手动执行exe下txt中命令");
                        }
                    });
                });
            }
        });
    }

    /**
     * 应用高亮通知样式和动画
     */
    private void applyHighlightNoticeStyle(HBox noticeBox, Label noticeLabel) {
        if (noticeBox == null || noticeLabel == null) return;
        
        // 停止可能正在运行的动画
        stopNoticeAnimation();
        
        // 设置高亮样式
        noticeLabel.setStyle("-fx-text-fill: #FFD700; -fx-font-weight: bold;");
        noticeBox.setStyle("-fx-background-color: rgba(255, 0, 0, 0.3); -fx-background-radius: 5;");
        
        // 创建呼吸灯效果动画
        noticeAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, 
                new KeyValue(noticeBox.opacityProperty(), 1.0),
                new KeyValue(noticeBox.scaleXProperty(), 1.0),
                new KeyValue(noticeBox.scaleYProperty(), 1.0)),
            new KeyFrame(Duration.seconds(1.0), 
                new KeyValue(noticeBox.opacityProperty(), 0.7),
                new KeyValue(noticeBox.scaleXProperty(), 1.02),
                new KeyValue(noticeBox.scaleYProperty(), 1.02)),
            new KeyFrame(Duration.seconds(2.0), 
                new KeyValue(noticeBox.opacityProperty(), 1.0),
                new KeyValue(noticeBox.scaleXProperty(), 1.0),
                new KeyValue(noticeBox.scaleYProperty(), 1.0))
        );
        
        noticeAnimation.setCycleCount(Timeline.INDEFINITE);
        noticeAnimation.play();
    }
    
    /**
     * 停止通知动画
     */
    private void stopNoticeAnimation() {
        if (noticeAnimation != null) {
            noticeAnimation.stop();
            if (noticeBox != null) {
                noticeBox.setOpacity(1.0);
                noticeBox.setScaleX(1.0);
                noticeBox.setScaleY(1.0);
            }
        }
    }

    /**
     * 从ApiService加载缓存的用户数据显示到UI
     */
    private void loadCachedUserData() {
        log.info("开始加载缓存的用户数据");
        Map<String, Object> homeData = apiService.getHomeData();
        if (homeData != null && !homeData.isEmpty()) {
            // 获取数据
            String username = homeData.getOrDefault("username", "").toString();
            String cursorExpdate = homeData.getOrDefault("cursorExpdate", "").toString();
            String email = homeData.getOrDefault("email", "").toString();
            // 高级模型使用量默认为无限制，忽略缓存数据
            String numRequests = "∞";
            String maxRequestUsage = "∞";
            String notice = homeData.getOrDefault("notice", "您的智能编程伙伴已就位，让我们高效构建代码吧！").toString();
            String gwurl = homeData.getOrDefault("gwurl", "").toString();
            String afterSaleGroupImg = homeData.getOrDefault("afterSaleGroupImg", "").toString();
            String cursorEmailCount = homeData.getOrDefault("cursorEmailCount", "").toString();
            String noticeHighlightFlag = homeData.getOrDefault("noticeHighlightFlag", "0").toString();
            
            log.info("缓存数据: 用户名={}, 过期时间={}, 通知={}", username, cursorExpdate, notice);
            
            // 获取用户类型和代理按钮控制字段
            String userType = apiService.getHomeData().getOrDefault("userType", "1").toString();
            String canClickQhdl = apiService.getHomeData().getOrDefault("canClickQhdl", "1").toString();

            // 确保在UI线程中执行更新
            if (Platform.isFxApplicationThread()) {
                updateUIWithUserData(username, cursorExpdate, email, numRequests, maxRequestUsage,
                                    notice, afterSaleGroupImg, cursorEmailCount, noticeHighlightFlag, userType, canClickQhdl);
            } else {
                // 更新UI
                Platform.runLater(() -> {
                    updateUIWithUserData(username, cursorExpdate, email, numRequests, maxRequestUsage,
                                        notice, afterSaleGroupImg, cursorEmailCount, noticeHighlightFlag, userType, canClickQhdl);
                });
            }
        } else {
            log.warn("无可用的缓存用户数据");
        }
    }
    
    /**
     * 使用获取的数据更新UI组件
     */
    private void updateUIWithUserData(String username, String cursorExpdate, String email,
                                     String numRequests, String maxRequestUsage, String notice,
                                     String afterSaleGroupImg, String cursorEmailCount, String noticeHighlightFlag, String userType, String canClickQhdl) {
        try {
            log.info("开始更新UI组件");
            // 更新欢迎信息
            if (welcomeTextLabel != null) {
                welcomeTextLabel.setText("您好, " + username);
                log.info("更新欢迎信息成功");
            } else {
                log.warn("welcomeTextLabel为null，无法更新");
            }
            
            // 更新用户信息
            if (userNameValue != null) userNameValue.setText(username);
            if (expDateValue != null) expDateValue.setText(cursorExpdate);
            if (emailCountValue != null) emailCountValue.setText(cursorEmailCount);

            // 天卡和次卡用户都固定显示邮箱记录按钮
            Button emailRecordBtn = (Button) lookup("#emailRecordBtn");
            if (emailRecordBtn != null) {
                // 天卡(1)和次卡(2)用户都显示按钮
//                boolean shouldShow = "2".equals(userType);
                emailRecordBtn.setVisible(true);
                log.info("邮箱记录按钮固定显示，用户类型: {}", userType);
            }

            // 根据canClickQhdl字段控制代理按钮的启用状态
            updateProxyButtonState(canClickQhdl);
            if (noticeLabel != null) {
                noticeLabel.setText(notice);
                // 根据noticeHighlightFlag设置高亮样式
                if ("1".equals(noticeHighlightFlag)) {
                    applyHighlightNoticeStyle(noticeBox, noticeLabel);
                } else {
                    // 停止可能正在运行的动画
                    stopNoticeAnimation();
                    noticeLabel.setStyle("-fx-text-fill: white;");
                    if (noticeBox != null) {
                        noticeBox.setStyle("");
                    }
                }
                log.info("更新通知信息成功");
            } else {
                log.warn("noticeLabel为null，无法更新");
            }
            
            // 更新底部标语信息
            if (bottomInfoLabel != null) {
                // 获取dibuinfo数据
                String dibuInfo = "";
                Map<String, Object> homeData = apiService.getHomeData();
                if (homeData != null && homeData.containsKey("dibuinfo")) {
                    dibuInfo = homeData.getOrDefault("dibuinfo", "").toString();
                }
                
                // 如果dibuinfo为空，使用默认文本
                if (StrUtil.isBlank(dibuInfo)) {
                    dibuInfo = "当前比较频繁问题会放公告，公告没有的则进官网查找解决方法";
                }
                
                bottomInfoLabel.setText(dibuInfo);
                log.info("更新底部标语成功: {}", dibuInfo);
            }
            
            // 更新邮箱信息
            if (emailValue != null) {
                emailValue.setText(email);
                log.info("更新邮箱信息成功: {}", email);
            }
            
            // 更新高级模型使用量为无限制
            if (advRequestsValue != null) {
                advRequestsValue.setText("∞/∞");
                log.info("更新使用量统计为无限制");
            }
            
            // 更新进度条为满进度绿色
            if (advProgressBar != null) {
                advProgressBar.setProgress(1.0);
                log.info("更新进度条为满进度");
            }
            
            // 获取账号级别和剩余时间数据
            Map<String, Object> homeData = apiService.getHomeData();
            
            // 更新账号级别
            String membershipType = homeData.getOrDefault("membershipType", "").toString();
            Label currentAccountTypeValue = (Label)lookup("#accountTypeValue");
            if (currentAccountTypeValue != null) {
                if (StrUtil.isNotBlank(membershipType)) {
                    currentAccountTypeValue.setText(membershipType);
                    log.info("更新账号级别成功: {}", membershipType);
                } else {
                    currentAccountTypeValue.setText("未获取");
                    log.warn("账号级别数据为空，设置为未获取状态");
                }
            } else {
                log.warn("无法找到accountTypeValue组件");
            }

            // 更新状态(根据剩余天数判断)
            String daysRemaining = homeData.getOrDefault("daysRemainingOnTrial", "").toString();
            Label currentRemainingTimeValue = (Label)lookup("#remainingTimeValue");
            if (currentRemainingTimeValue != null) {
                if (StrUtil.isNotBlank(daysRemaining) && NumberUtil.isNumber(daysRemaining)) {
                    int days = Integer.parseInt(daysRemaining);
                    if (days > 0) {
                        currentRemainingTimeValue.setText("有效");
                        currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #67C23A; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;"); // 绿色表示有效
                    } else {
                        currentRemainingTimeValue.setText("过期");
                        currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #F56C6C; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;"); // 红色表示过期
                    }
                    log.info("更新状态成功: 剩余天数={}, 状态={}", daysRemaining, days > 0 ? "有效" : "过期");
                } else {
                    currentRemainingTimeValue.setText("未知");
                    currentRemainingTimeValue.setStyle("-fx-font-weight: bold; -fx-text-fill: #909399; -fx-background-color: #f5f5f5; -fx-padding: 5 10; -fx-background-radius: 4;");
                    log.warn("剩余天数数据无效，设置为未知状态");
                }
            } else {
                log.warn("无法找到remainingTimeValue组件");
            }
            
            // 更新售后群图片
            updateAfterSaleGroupImage(afterSaleGroupImg);
            log.info("UI组件更新完成");
        } catch (Exception e) {
            log.error("更新缓存数据到UI出错", e);
        }
    }

    /**
     * 官网按钮点击事件
     */
    private void onOfficialWebsiteBtnClicked() {
        try {
            // 尝试先从缓存中获取官网地址
            Map<String, Object> homeData = apiService.getHomeData();
            String gwurl = "";
            
            if (homeData != null && homeData.containsKey("gwurl")) {
                gwurl = homeData.getOrDefault("gwurl", "").toString();
            }
            
            // 如果缓存中没有，则重新请求
            if (StrUtil.isBlank(gwurl)) {
                CommonResult<JSONObject> result = apiService.getUserInfo();
                if (!result.getIsSuccess()) {
                    log.error("获取官网地址失败: {}", result.getMsg());
                    return;
                }
                
                gwurl = result.getData().getOrDefault("gwurl", "").toString();
                
                // 缓存官网地址
                if (StrUtil.isNotBlank(gwurl)) {
                    apiService.updateHomeData("gwurl", gwurl);
                }
            }
            
            // 检查官网地址是否为空
            if (StrUtil.isBlank(gwurl)) {
                log.info("官网地址为空，不执行任何操作");
                return;
            }
            
            log.info("准备打开官网: {}", gwurl);
            
            // 使用默认浏览器打开官网
            try {
                java.awt.Desktop.getDesktop().browse(new java.net.URI(gwurl));
                showToast("正在打开官网...", 2000);
            } catch (Exception e) {
                log.error("打开官网失败", e);
                showAlert("打开官网失败", "无法打开默认浏览器，请手动访问");
            }
        } catch (Exception e) {
            log.error("访问官网出错", e);
        }
    }

    /**
     * 加载公告信息
     */
    private void loadPublicNotice() {
        try {
            if (publicNoticeShown) {
                log.info("公告已显示过，不再显示");
                return; // 避免重复显示公告
            }

            // 调用API获取公告信息
            log.info("开始获取公告信息");
            CommonResult<JSONObject> result = apiService.getPublicNotice();
            if (result.getIsSuccess() && result.getData() != null) {
                JSONObject noticeData = result.getData();
                String title = noticeData.getStr("title");
                String content = noticeData.getStr("content");
                log.info("获取公告成功，标题：{}，内容长度：{}", title, 
                         content != null ? content.length() : 0);

                if (StrUtil.isNotBlank(title) && StrUtil.isNotBlank(content)) {
                    // 在UI线程中创建和显示公告弹窗
                    Platform.runLater(() -> {
                        try {
                            log.info("准备显示公告弹窗");
                            showNoticeDialog(title, content);
                            publicNoticeShown = true;
                            log.info("公告弹窗显示成功");
                        } catch (Exception e) {
                            log.error("显示公告弹窗时出错", e);
                        }
                    });
                } else {
                    log.info("公告标题或内容为空，不显示弹窗");
                }
            } else {
                log.warn("获取公告失败或无公告数据：{}", result.getMsg());
            }
        } catch (Exception e) {
            log.error("加载公告信息出错", e);
        }
    }

    /**
     * 显示公告弹窗
     * @param title 公告标题
     * @param content 公告内容
     */
    private void showNoticeDialog(String title, String content) {
        try {
            log.info("准备显示公告弹窗");
            
            // 获取主窗口的内容面板
            Pane mainContentPane = contentPane;
            
            // 创建半透明背景遮罩
            Rectangle overlay = new Rectangle();
            overlay.setWidth(mainContentPane.getWidth());
            overlay.setHeight(mainContentPane.getHeight());
            overlay.setFill(Color.rgb(0, 0, 0, 0.5)); // 半透明黑色
            
            // 创建公告内容容器 - 增大尺寸，优化样式
            VBox noticeContent = new VBox(0);
            noticeContent.setMaxWidth(650);  // 从450增加到650
            noticeContent.setMaxHeight(500); // 从320增加到500
            noticeContent.setStyle("-fx-background-color: white; -fx-background-radius: 12; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 20, 0, 0, 8);");
            
            // 创建标题栏 - 优化样式和布局
            HBox titleBar = new HBox();
            titleBar.setPadding(new Insets(20, 25, 15, 25));
            titleBar.setStyle("-fx-background-color: linear-gradient(to right, #409EFF, #66b1ff); -fx-background-radius: 12 12 0 0;");
            titleBar.setAlignment(Pos.CENTER_LEFT);

            // 标题文本 - 增大字体，优化字体族
            Label titleLabel = new Label(title);
            titleLabel.setFont(Font.font("Microsoft YaHei", FontWeight.BOLD, 20));
            titleLabel.setTextFill(Color.WHITE);
            HBox.setHgrow(titleLabel, Priority.ALWAYS);

            // 关闭按钮 - 优化样式和交互效果
            Button closeButton = new Button("✕");
            closeButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 16px; -fx-cursor: hand; -fx-padding: 5; -fx-min-width: 30; -fx-min-height: 30; -fx-background-radius: 15;");
            closeButton.setOnMouseEntered(e -> closeButton.setStyle("-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 16px; -fx-cursor: hand; -fx-padding: 5; -fx-min-width: 30; -fx-min-height: 30; -fx-background-radius: 15;"));
            closeButton.setOnMouseExited(e -> closeButton.setStyle("-fx-background-color: transparent; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 16px; -fx-cursor: hand; -fx-padding: 5; -fx-min-width: 30; -fx-min-height: 30; -fx-background-radius: 15;"));
            
            // 添加到标题栏
            titleBar.getChildren().addAll(titleLabel, closeButton);
            
            // 创建内容区域 - 增加间距和内边距
            VBox contentBox = new VBox(15);
            contentBox.setPadding(new Insets(20, 25, 20, 25));

            // 添加公告文本区域 - 优化样式，增大高度，改善可读性
            TextArea textArea = new TextArea(content);
            textArea.setWrapText(true);
            textArea.setEditable(false);
            textArea.setPrefHeight(320); // 从200增加到320
            textArea.setStyle("-fx-control-inner-background: #fafafa; -fx-background-color: #fafafa; -fx-border-color: #e4e7ed; -fx-border-radius: 6; -fx-border-width: 1; -fx-focus-color: transparent; -fx-font-size: 14px; -fx-font-family: 'Microsoft YaHei', 'SimSun', sans-serif; -fx-text-fill: #303133; -fx-padding: 15; -fx-line-spacing: 2px;");
            
            // 按钮容器 - 优化布局
            HBox buttonBox = new HBox();
            buttonBox.setAlignment(Pos.CENTER);
            buttonBox.setPadding(new Insets(15, 0, 10, 0));

            // 确认按钮 - 优化样式和尺寸
            Button confirmButton = new Button("我知道了");
            confirmButton.setPrefWidth(120);
            confirmButton.setPrefHeight(36);
            confirmButton.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px; -fx-font-family: 'Microsoft YaHei', 'SimSun', sans-serif; -fx-background-radius: 6; -fx-cursor: hand; -fx-effect: dropshadow(three-pass-box, rgba(64,158,255,0.3), 4, 0, 0, 2);");

            // 鼠标悬停效果 - 增强视觉反馈
            confirmButton.setOnMouseEntered(e -> confirmButton.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px; -fx-font-family: 'Microsoft YaHei', 'SimSun', sans-serif; -fx-background-radius: 6; -fx-cursor: hand; -fx-effect: dropshadow(three-pass-box, rgba(102,177,255,0.4), 6, 0, 0, 3);"));
            confirmButton.setOnMouseExited(e -> confirmButton.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px; -fx-font-family: 'Microsoft YaHei', 'SimSun', sans-serif; -fx-background-radius: 6; -fx-cursor: hand; -fx-effect: dropshadow(three-pass-box, rgba(64,158,255,0.3), 4, 0, 0, 2);"));
            
            buttonBox.getChildren().add(confirmButton);
            
            // 将所有组件添加到内容区域
            contentBox.getChildren().addAll(textArea, buttonBox);
            
            // 添加标题栏和内容区域到公告容器
            noticeContent.getChildren().addAll(titleBar, contentBox);
            
            // 创建一个堆叠面板来包含遮罩和公告内容
            StackPane dialogContainer = new StackPane();
            dialogContainer.getChildren().addAll(overlay, noticeContent);
            
            // 设置堆叠面板占满整个内容区域
            dialogContainer.setPrefSize(mainContentPane.getWidth(), mainContentPane.getHeight());
            dialogContainer.setMaxSize(Region.USE_COMPUTED_SIZE, Region.USE_COMPUTED_SIZE);
            StackPane.setAlignment(noticeContent, Pos.CENTER); // 将公告内容居中显示
            
            // 设置初始不透明度为0（用于淡入效果）
            dialogContainer.setOpacity(0);
            
            // 添加到主内容面板
            mainContentPane.getChildren().add(dialogContainer);
            
            // 关闭和确认按钮的事件处理
            closeButton.setOnAction(e -> removeNoticeDialog(mainContentPane, dialogContainer));
            confirmButton.setOnAction(e -> removeNoticeDialog(mainContentPane, dialogContainer));
            
            // 创建淡入动画
            FadeTransition fadeIn = new FadeTransition(Duration.millis(300), dialogContainer);
            fadeIn.setFromValue(0);
            fadeIn.setToValue(1);
            fadeIn.play();
            
            log.info("公告弹窗已添加到内容面板，位置居中");
            
        } catch (Exception e) {
            log.error("显示公告弹窗出错", e);
        }
    }
    
    /**
     * 移除公告弹窗
     * @param mainContentPane 主内容面板
     * @param dialogContainer 弹窗容器
     */
    private void removeNoticeDialog(Pane mainContentPane, StackPane dialogContainer) {
        // 创建淡出动画
        FadeTransition fadeOut = new FadeTransition(Duration.millis(300), dialogContainer);
        fadeOut.setFromValue(1);
        fadeOut.setToValue(0);
        fadeOut.setOnFinished(event -> mainContentPane.getChildren().remove(dialogContainer));
        fadeOut.play();
    }

    /**
     * 确保对话框在父窗口中居中显示的通用方法
     * @param dialogStage 需要居中的对话框
     * @param parentStage 父窗口
     */
    private void centerDialogInParentStage(Stage dialogStage, Stage parentStage) {
        // 直接使用JavaFX内置的居中方法
        dialogStage.centerOnScreen();
        
        // 也可以相对于父窗口居中
        if (parentStage != null) {
            double parentX = parentStage.getX();
            double parentY = parentStage.getY();
            double parentWidth = parentStage.getWidth();
            double parentHeight = parentStage.getHeight();
            
            double dialogWidth = dialogStage.getWidth();
            double dialogHeight = dialogStage.getHeight();
            
            double newX = parentX + (parentWidth - dialogWidth) / 2;
            double newY = parentY + (parentHeight - dialogHeight) / 2;
            
            // 设置位置
            dialogStage.setX(newX);
            dialogStage.setY(newY);
            
            log.info("窗口居中: 父窗口位置={},{}，尺寸={}x{}, 对话框位置={},{}", 
                    parentX, parentY, parentWidth, parentHeight, newX, newY);
        }
        
        // 确保窗口可见
        dialogStage.setOpacity(1);
    }
    
    // 用于窗口拖动的辅助类
    private static class Delta {
        double x, y;
    }
    
    /**
     * 检查应用程序是否以管理员权限运行（带缓存机制）
     * @return 是否以管理员权限运行
     */
    private boolean isRunAsAdministrator() {
        // 如果已经检测过，直接返回缓存结果
        if (isAdminCache != null) {
            return isAdminCache;
        }
        
        try {
            String os = System.getProperty("os.name").toLowerCase();
            
            // Windows系统特定检测方法
            if (os.contains("win")) {
                // 尝试写入到一个需要管理员权限的位置
                // 这里只是简单示例，可能需要根据实际情况调整
                String windir = System.getenv("windir");
                File testFile = new File(windir + File.separator + "temp.test");
                
                if (!testFile.exists()) {
                    try {
                        // 尝试创建文件
                        boolean canCreate = testFile.createNewFile();
                        if (canCreate) {
                            // 如果能创建，则删除并返回true
                            testFile.delete();
                            isAdminCache = true;
                            return true;
                        }
                        isAdminCache = false;
                        return false;
                    } catch (Exception e) {
                        isAdminCache = false;
                        return false;
                    }
                }
                
                // 如果文件已存在，尝试写入
                try {
                    FileUtil.writeUtf8String("test", testFile);
                    testFile.delete();
                    isAdminCache = true;
                    return true;
                } catch (Exception e) {
                    isAdminCache = false;
                    return false;
                }
            } 
            // Linux/Mac系统可添加其他检测方法
            
            isAdminCache = false;
            return false;
        } catch (Exception e) {
            log.error("检测管理员权限时出错", e);
            isAdminCache = false;
            return false;
        }
    }
    /**
     * 根据ID查找UI元素
     */
    private javafx.scene.Node lookup(String id) {
        if (contentPane != null) {
            return contentPane.lookup(id);
        }
        return null;
    }

    /**
     * 根据canClickQhdl字段更新代理按钮的启用状态
     */
    private void updateProxyButtonState(String canClickQhdl) {
        try {
            // 查找代理按钮（第一排第二个按钮）
            Button switchAccountNoResetBtn = (Button) lookup("#switchAccountNoResetBtn");

            if (switchAccountNoResetBtn != null) {
                boolean canClick = !"0".equals(canClickQhdl); // canClickQhdl为0时禁用按钮

                switchAccountNoResetBtn.setDisable(!canClick);

                if (!canClick) {
                    // 禁用状态的样式
                    switchAccountNoResetBtn.setStyle("-fx-background-color: #c0c4cc; -fx-text-fill: #909399; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: default;");
                    // 移除悬停效果
                    switchAccountNoResetBtn.setOnMouseEntered(null);
                    switchAccountNoResetBtn.setOnMouseExited(null);
                    log.info("代理按钮已禁用，canClickQhdl: {}", canClickQhdl);
                } else {
                    // 启用状态的样式
                    switchAccountNoResetBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;");
                    // 恢复悬停效果
                    switchAccountNoResetBtn.setOnMouseEntered(e ->
                        switchAccountNoResetBtn.setStyle("-fx-background-color: #66b1ff; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
                    switchAccountNoResetBtn.setOnMouseExited(e ->
                        switchAccountNoResetBtn.setStyle("-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: normal; -fx-background-radius: 4; -fx-cursor: hand;"));
                    log.info("代理按钮已启用，canClickQhdl: {}", canClickQhdl);
                }
            } else {
                log.warn("未找到代理按钮，无法更新状态");
            }
        } catch (Exception e) {
            log.error("更新代理按钮状态失败", e);
        }
    }



    private static JSONObject getEmailInfo(String accessToken) {
        HttpRequest doget = HttpUtil.createGet("https://api2.cursor.sh/auth/full_stripe_profile");
        doget.header("Authorization", "Bearer " + accessToken);
        HttpResponse execute = doget.execute();
        String body = execute.body();
        log.info("获取账号信息返回数据: {}", body);
        if (StrUtil.isNotBlank(body)) {
            JSONObject obj = JSONUtil.parseObj(body);
            return obj;
        }
        return null;
    }

    /**
     * 显示邮箱记录对话框
     */
    private void showEmailRecordDialog() {
        try {
            // 创建新的Stage作为对话框
            Stage dialogStage = new Stage();
            dialogStage.setTitle("📧 邮箱使用记录(近3天)");
            dialogStage.initModality(Modality.APPLICATION_MODAL);
            dialogStage.setResizable(true);
            dialogStage.setWidth(1000);
            dialogStage.setHeight(700);
            dialogStage.setMinWidth(800);
            dialogStage.setMinHeight(600);

            // 居中显示
            dialogStage.centerOnScreen();

            // 创建邮箱记录视图控制器
            EmailRecordDialogCtrl dialogCtrl = new EmailRecordDialogCtrl(apiService, dialogStage);

            // 创建场景并显示
            Scene scene = new Scene(dialogCtrl.createContent());
            try {
                scene.getStylesheets().add(getClass().getResource("/css/dialog.css").toExternalForm());
            } catch (Exception cssEx) {
                log.warn("无法加载CSS样式文件，使用默认样式");
            }
            dialogStage.setScene(scene);
            dialogStage.showAndWait();

        } catch (Exception e) {
            log.error("显示邮箱记录对话框失败", e);
            showAlert("错误", "显示邮箱记录失败：" + e.getMessage());
        }
    }
}
