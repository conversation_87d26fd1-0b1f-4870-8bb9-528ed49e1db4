<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox alignment="TOP_CENTER" prefWidth="840.0" prefHeight="700.0" spacing="20.0" style="-fx-background-color: #f5f5f5;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.ceadeal.javafxboot.ctrl.ChangePasswordCtrl">
   <padding>
      <Insets top="40.0" right="40.0" bottom="40.0" left="40.0" />
   </padding>
   <children>
      <Label text="修改密码" textAlignment="CENTER" style="-fx-text-fill: #303133;">
         <font>
            <Font size="24.0" />
         </font>
         <VBox.margin>
            <Insets bottom="20.0" />
         </VBox.margin>
      </Label>
      <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 4; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" maxWidth="500.0">
         <padding>
            <Insets top="30.0" right="30.0" bottom="30.0" left="30.0" />
         </padding>
         <children>
            <Label text="为了账户安全，请修改您的密码" textAlignment="CENTER" style="-fx-text-fill: #606266;">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <PasswordField fx:id="oldPasswordField" promptText="当前密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets top="15.0" />
               </VBox.margin>
            </PasswordField>
            <PasswordField fx:id="newPasswordField" promptText="新密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets top="15.0" />
               </VBox.margin>
            </PasswordField>
            <PasswordField fx:id="confirmPasswordField" promptText="确认新密码" maxWidth="300.0" prefHeight="40.0" style="-fx-background-color: white; -fx-border-color: #DCDFE6; -fx-border-radius: 4; -fx-padding: 8 12; -fx-prompt-text-fill: #909399;">
               <VBox.margin>
                  <Insets top="15.0" />
               </VBox.margin>
            </PasswordField>
            <Button fx:id="changePasswordButton" defaultButton="true" mnemonicParsing="false" onAction="#onChangePasswordButtonClicked" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #409EFF; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-cursor: hand;" text="确认修改">
               <VBox.margin>
                  <Insets top="20.0" />
               </VBox.margin>
            </Button>
         </children>
      </VBox>
      <Label text="提示: 密码应包含字母、数字和特殊字符，长度不少于8位" style="-fx-text-fill: #909399;">
         <font>
            <Font size="12.0" />
         </font>
         <VBox.margin>
            <Insets top="20.0" />
         </VBox.margin>
      </Label>
   </children>
</VBox> 