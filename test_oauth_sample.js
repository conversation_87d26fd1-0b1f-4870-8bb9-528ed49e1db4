// 测试用的 JavaScript 文件 - 模拟您提供的格式

async createOAuthState(){
    let t=qQe((0,Lv.randomBytes)(32)),
        r=qQe(xZt(Buffer.from(t))),
        a=(0,Lv.randomUUID)(),
        n={codeVerifier:t,codeChallenge:r,state:a,creationTime:new Date().getTime()};
    return await this._context.secrets.store(lN,JSON.stringify(n));
}

// 另一种可能的格式
function createOAuthState2() {
    const verifier = generateVerifier();
    const challenge = generateChallenge(verifier);
    const stateValue = generateState();
    const result = {
        codeVerifier: verifier,
        codeChallenge: challenge,
        state: stateValue,
        creationTime: new Date().getTime()
    };
    return result;
}

// 更紧凑的格式
const createOAuthState3 = () => {
    let x=random(),y=hash(x),z=uuid();
    return {codeVerifier:x,codeChallenge:y,state:z,creationTime:Date.now()};
}
