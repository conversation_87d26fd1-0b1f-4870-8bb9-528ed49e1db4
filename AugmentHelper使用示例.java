package com.ceadeal.javafxboot.util;

/**
 * AugmentHelper 使用示例
 * 
 * 这个示例展示了如何使用 AugmentHelper 类来修改 JavaScript 文件中的 createOAuthState 方法
 */
public class AugmentHelperExample {

    public static void main(String[] args) {
        // 示例1: 使用基本方法修改 OAuth 状态
        basicExample();
        
        // 示例2: 使用正则表达式方法
        regexExample();
        
        // 示例3: 使用简单替换方法
        simpleExample();
    }

    /**
     * 基本使用示例
     */
    public static void basicExample() {
        System.out.println("=== 基本方法示例 ===");
        
        String jsFilePath = "path/to/your/oauth.js";
        String codeVerifier = "my_fixed_code_verifier_12345";
        String codeChallenge = "my_fixed_code_challenge_67890";
        String state = "my_fixed_state_abcdef";
        
        try {
            boolean result = AugmentHelper.modifyCreateOAuthState(
                jsFilePath, 
                codeVerifier, 
                codeChallenge, 
                state
            );
            
            if (result) {
                System.out.println("✅ 成功修改 JavaScript 文件");
                System.out.println("📁 文件路径: " + jsFilePath);
                System.out.println("🔑 codeVerifier: " + codeVerifier);
                System.out.println("🔐 codeChallenge: " + codeChallenge);
                System.out.println("🎯 state: " + state);
            } else {
                System.out.println("❌ 修改失败，请检查文件路径和内容格式");
            }
        } catch (Exception e) {
            System.err.println("💥 发生异常: " + e.getMessage());
        }
    }

    /**
     * 正则表达式方法示例
     */
    public static void regexExample() {
        System.out.println("\n=== 正则表达式方法示例 ===");
        
        String jsFilePath = "path/to/your/oauth.js";
        String codeVerifier = "regex_code_verifier_xyz";
        String codeChallenge = "regex_code_challenge_abc";
        String state = "regex_state_123";
        
        boolean result = AugmentHelper.modifyCreateOAuthStateWithRegex(
            jsFilePath, 
            codeVerifier, 
            codeChallenge, 
            state
        );
        
        System.out.println("正则表达式修改结果: " + (result ? "成功" : "失败"));
    }

    /**
     * 简单替换方法示例
     */
    public static void simpleExample() {
        System.out.println("\n=== 简单替换方法示例 ===");
        
        String jsFilePath = "path/to/your/oauth.js";
        String codeVerifier = "simple_verifier_999";
        String codeChallenge = "simple_challenge_888";
        String state = "simple_state_777";
        
        boolean result = AugmentHelper.modifyCreateOAuthStateSimple(
            jsFilePath, 
            codeVerifier, 
            codeChallenge, 
            state
        );
        
        System.out.println("简单替换修改结果: " + (result ? "成功" : "失败"));
    }

    /**
     * 批量处理示例
     */
    public static void batchProcessExample() {
        System.out.println("\n=== 批量处理示例 ===");
        
        // 要处理的文件列表
        String[] jsFiles = {
            "path/to/oauth1.js",
            "path/to/oauth2.js", 
            "path/to/oauth3.js"
        };
        
        // 固定的 OAuth 参数
        String codeVerifier = "batch_code_verifier";
        String codeChallenge = "batch_code_challenge";
        String state = "batch_state";
        
        int successCount = 0;
        int totalCount = jsFiles.length;
        
        for (String filePath : jsFiles) {
            System.out.println("正在处理: " + filePath);
            
            boolean result = AugmentHelper.modifyCreateOAuthState(
                filePath, 
                codeVerifier, 
                codeChallenge, 
                state
            );
            
            if (result) {
                successCount++;
                System.out.println("  ✅ 成功");
            } else {
                System.out.println("  ❌ 失败");
            }
        }
        
        System.out.println("\n📊 批量处理结果:");
        System.out.println("总文件数: " + totalCount);
        System.out.println("成功数量: " + successCount);
        System.out.println("失败数量: " + (totalCount - successCount));
        System.out.println("成功率: " + String.format("%.1f%%", (double) successCount / totalCount * 100));
    }

    /**
     * 带错误处理的完整示例
     */
    public static void completeExample() {
        System.out.println("\n=== 完整示例（带错误处理）===");
        
        String jsFilePath = "path/to/your/oauth.js";
        String codeVerifier = "complete_verifier_2024";
        String codeChallenge = "complete_challenge_2024";
        String state = "complete_state_2024";
        
        try {
            // 检查文件是否存在
            java.io.File file = new java.io.File(jsFilePath);
            if (!file.exists()) {
                System.err.println("❌ 文件不存在: " + jsFilePath);
                return;
            }
            
            // 备份原文件
            String backupPath = jsFilePath + ".backup." + System.currentTimeMillis();
            java.nio.file.Files.copy(
                file.toPath(), 
                new java.io.File(backupPath).toPath()
            );
            System.out.println("📋 已创建备份文件: " + backupPath);
            
            // 执行修改
            boolean result = AugmentHelper.modifyCreateOAuthState(
                jsFilePath, 
                codeVerifier, 
                codeChallenge, 
                state
            );
            
            if (result) {
                System.out.println("✅ 修改成功完成");
                System.out.println("💾 原文件已备份到: " + backupPath);
            } else {
                System.out.println("❌ 修改失败，正在恢复原文件...");
                // 恢复备份
                java.nio.file.Files.copy(
                    new java.io.File(backupPath).toPath(),
                    file.toPath(),
                    java.nio.file.StandardCopyOption.REPLACE_EXISTING
                );
                System.out.println("🔄 已恢复原文件");
            }
            
        } catch (Exception e) {
            System.err.println("💥 处理过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
