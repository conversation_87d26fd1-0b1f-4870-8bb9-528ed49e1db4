package com.ceadeal.javafxboot.ctrl;

import com.ceadeal.javafxboot.Application;
import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.view.LoginView;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.control.Button;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 注册页面控制器
 */
@Slf4j
@FXMLController
public class RegisterCtrl implements Initializable {

    @FXML
    private TextField usernameField;

    @FXML
    private PasswordField passwordField;
    
    @FXML
    private PasswordField confirmPasswordField;

    @FXML
    private Button registerButton;
    
    @FXML
    private Button backToLoginButton;

    @Autowired
    private ApiService apiService;

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Register view initialized");
    }

    /**
     * 注册按钮点击事件
     */
    @FXML
    private void onRegisterButtonClicked(ActionEvent event) {
        String username = usernameField.getText().trim();
        String password = passwordField.getText().trim();
        String confirmPassword = confirmPasswordField.getText().trim();
        
        // 输入验证
        if (username.isEmpty() || password.isEmpty() || confirmPassword.isEmpty()) {
            showAlert(AlertType.WARNING, "提示", "所有字段都必须填写！");
            return;
        }
        
        // 密码匹配验证
        if (!password.equals(confirmPassword)) {
            showAlert(AlertType.ERROR, "错误", "两次输入的密码不一致！");
            return;
        }
        
        // 密码长度验证
        if (password.length() < 4) {
            showAlert(AlertType.WARNING, "提示", "密码长度不能少于4个字符！");
            return;
        }
        
        // 调用注册接口
        CommonResult result = apiService.register(username, password);
        if (result.getIsSuccess()) {
            showAlert(AlertType.INFORMATION, "注册成功", "您已成功注册，请登录！");
            // 注册成功后返回登录页面
            Application.showView(LoginView.class);
        } else {
            showAlert(AlertType.ERROR, "注册失败", result.getMsg());
        }
    }
    
    /**
     * 返回登录按钮点击事件
     */
    @FXML
    private void onBackToLoginButtonClicked(ActionEvent event) {
        // 返回登录页面
        Application.showView(LoginView.class);
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(AlertType alertType, String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(alertType);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }
} 