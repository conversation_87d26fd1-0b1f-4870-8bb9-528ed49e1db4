package com.ceadeal.javafxboot.ctrl;

import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.service.ApiService;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.PasswordField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 修改密码控制器
 */
@Slf4j
@FXMLController
public class ChangePasswordCtrl implements Initializable {

    @FXML
    private PasswordField oldPasswordField;

    @FXML
    private PasswordField newPasswordField;

    @FXML
    private PasswordField confirmPasswordField;

    @FXML
    private Button changePasswordButton;

    @Autowired
    private ApiService apiService;

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("ChangePassword view initialized");
    }

    /**
     * 修改密码按钮点击事件
     */
    @FXML
    private void onChangePasswordButtonClicked(ActionEvent event) {
        String oldPassword = oldPasswordField.getText().trim();
        String newPassword = newPasswordField.getText().trim();
        String confirmPassword = confirmPasswordField.getText().trim();
        
        // 输入验证
        if (oldPassword.isEmpty() || newPassword.isEmpty() || confirmPassword.isEmpty()) {
            showAlert("提示", "所有密码字段不能为空！");
            return;
        }
        
        if (!newPassword.equals(confirmPassword)) {
            showAlert("提示", "新密码与确认密码不一致！");
            return;
        }
        
        // 调用修改密码接口
        CommonResult result = apiService.changePassword(oldPassword, newPassword);
        
        if (result.getIsSuccess()) {
            showAlert("成功", "密码修改成功！");
            oldPasswordField.clear();
            newPasswordField.clear();
            confirmPasswordField.clear();
        } else {
            showAlert("失败", "密码修改失败，请检查旧密码是否正确！");
        }
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }
} 