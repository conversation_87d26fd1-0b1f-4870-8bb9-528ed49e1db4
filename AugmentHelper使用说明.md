# AugmentHelper 使用说明

## 概述

`AugmentHelper` 是一个用于修改 JavaScript 文件中 `createOAuthState` 方法的工具类。它可以将该方法中的 `codeVerifier`、`codeChallenge`、`state` 值替换为固定的传入值。

## 功能特点

- 使用 Hutool 库按行读取 JavaScript 文件
- 支持多种修改策略：基本方法、正则表达式方法、简单替换方法
- 保持原有代码格式和缩进
- 提供详细的日志输出
- 支持错误处理和异常捕获

## 方法说明

### 1. modifyOAuthStateAdvancedRegex (强烈推荐)

**描述**: 专门针对您提供格式的高级正则表达式方法，支持可变变量名和灵活的空格处理。

**特点**:
- 支持可变变量名（如 `t`, `r`, `a` 或任何其他变量名）
- 灵活处理空格和格式变化
- 支持变量赋值（如 `n={...}` 或 `let obj = {...}`）
- 保持原有代码格式和缩进

**参数**:
- `jsFilePath`: JavaScript 文件路径
- `codeVerifier`: 固定的 codeVerifier 值
- `codeChallenge`: 固定的 codeChallenge 值
- `state`: 固定的 state 值

**返回值**: `boolean` - 是否修改成功

**使用示例**:
```java
boolean result = AugmentHelper.modifyOAuthStateAdvancedRegex(
    "path/to/your/file.js",
    "fixed_code_verifier_123",
    "fixed_code_challenge_456",
    "fixed_state_789"
);
```

### 2. modifyCreateOAuthStateWithRegex

**描述**: 基本的正则表达式方法，适用于标准格式的 OAuth 状态对象。

**参数**: 同上

**使用示例**:
```java
boolean result = AugmentHelper.modifyCreateOAuthStateWithRegex(
    "path/to/your/file.js",
    "regex_code_verifier_123",
    "regex_code_challenge_456",
    "regex_state_789"
);
```

### 3. modifyCreateOAuthState

**描述**: 逐行解析的基本方法，通过大括号计数确定方法范围。

**参数**: 同上

**使用示例**:
```java
boolean result = AugmentHelper.modifyCreateOAuthState(
    "path/to/your/file.js",
    "basic_code_verifier_123",
    "basic_code_challenge_456",
    "basic_state_789"
);
```

## 原始代码示例

工具类设计用于处理类似以下格式的 JavaScript 代码：

```javascript
async createOAuthState(){
    let t=qQe((0,Lv.randomBytes)(32)),
        r=qQe(xZt(Buffer.from(t))),
        a=(0,Lv.randomUUID)(),
        n={
            codeVerifier:t,
            codeChallenge:r,
            state:a,
            creationTime:new Date().getTime()
        };
    return await this._context.secrets.store(lN,JSON.stringify(n));
}
```

## 修改后的代码示例

修改后的代码将变成：

```javascript
async createOAuthState(){
    let t=qQe((0,Lv.randomBytes)(32)),
        r=qQe(xZt(Buffer.from(t))),
        a=(0,Lv.randomUUID)(),
        n={
            codeVerifier:"fixed_code_verifier_123",
            codeChallenge:"fixed_code_challenge_456", 
            state:"fixed_state_789",
            creationTime:new Date().getTime()
        };
    return await this._context.secrets.store(lN,JSON.stringify(n));
}
```

## 注意事项

1. **文件备份**: 建议在修改前备份原始文件
2. **编码格式**: 默认使用 UTF-8 编码读写文件
3. **错误处理**: 所有方法都包含异常处理，会在日志中输出错误信息
4. **方法选择**:
   - **强烈推荐使用 `modifyOAuthStateAdvancedRegex` 方法**，专门针对您的格式优化
   - 如果高级正则方法无法处理，可尝试基本正则表达式方法
   - 逐行解析方法作为备选方案

## 测试

项目包含完整的测试类 `AugmentHelperTest`，可以运行测试来验证功能：

```bash
# 运行测试
mvn test -Dtest=AugmentHelperTest

# 或者直接运行 main 方法
java -cp target/classes:target/test-classes com.ceadeal.javafxboot.util.AugmentHelperTest
```

## 依赖

- Hutool 5.8.32 (已在 pom.xml 中配置)
- Lombok (用于日志注解)
- JUnit 5 (用于测试)

## 日志输出

工具类使用 Lombok 的 `@Slf4j` 注解提供日志功能，会输出以下信息：
- 找到方法的行号
- 修改的具体内容
- 成功或失败的状态
- 错误详情（如果有）
