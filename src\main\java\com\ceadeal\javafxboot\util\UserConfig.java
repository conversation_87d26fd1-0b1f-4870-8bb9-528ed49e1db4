package com.ceadeal.javafxboot.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户配置类
 * 用于保存用户登录信息
 */
@Slf4j
@Data
@Component
public class UserConfig {
    private static final String CONFIG_FILE = "user.config";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_PASSWORD = "password";
    private static final String KEY_REMEMBER = "remember";
    
    private String username;
    private String password;
    private boolean remember;
    
    /**
     * 加载配置
     */
    public void loadConfig() {
        try {
            File file = new File(CONFIG_FILE);
            if (!file.exists()) {
                return;
            }
            
            String content = FileUtil.readString(file, StandardCharsets.UTF_8);
            if (StrUtil.isBlank(content)) {
                return;
            }
            
            String[] lines = content.split("\n");
            for (String line : lines) {
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (KEY_USERNAME.equals(parts[0])) {
                        this.username = parts[1];
                    } else if (KEY_PASSWORD.equals(parts[0])) {
                        this.password = decrypt(parts[1]);
                    } else if (KEY_REMEMBER.equals(parts[0])) {
                        this.remember = "true".equals(parts[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.error("加载配置出错", e);
        }
    }
    
    /**
     * 保存配置
     */
    public void saveConfig() {
        try {
            Map<String, String> config = new HashMap<>();
            config.put(KEY_USERNAME, username);
            
            if (remember && password != null) {
                config.put(KEY_PASSWORD, encrypt(password));
            } else {
                config.put(KEY_PASSWORD, "");
            }
            
            config.put(KEY_REMEMBER, String.valueOf(remember));
            
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : config.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("\n");
            }
            
            FileUtil.writeString(sb.toString(), CONFIG_FILE, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("保存配置出错", e);
        }
    }
    
    /**
     * 清除配置
     */
    public void clearConfig() {
        this.username = null;
        this.password = null;
        this.remember = false;
        FileUtil.del(CONFIG_FILE);
    }
    
    // 简单加密
    private String encrypt(String text) {
        return SecureUtil.md5(text);
    }
    
    // 这里无法解密，但因为我们用md5加密后只能比较，所以实际应用中应该使用可逆加密算法
    private String decrypt(String encryptedText) {
        return encryptedText;
    }
} 