package com.ceadeal.javafxboot.ctrl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.ceadeal.javafxboot.Application;
import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.view.LoginView;
import de.felixroske.jfxsupport.FXMLController;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.TextArea;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

/**
 * 公告信息页面控制器
 */
@Slf4j
@FXMLController
public class NoticeCtrl implements Initializable {
    
    @FXML
    private TextArea noticeContentText;
    
    @FXML
    private TextArea helpContentText;
    
    @FXML
    private Button refreshButton;
    
    @FXML
    private Button backButton;
    
    @Autowired
    private ApiService apiService;
    
    private boolean isLoading = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Notice view initialized");
        
        // 设置文本区域样式
        setupTextAreas();
        
        // 初始化加载公告信息
        refreshNotice();
    }
    
    /**
     * 设置文本区域样式
     */
    private void setupTextAreas() {
        // 配置文本区域样式
        String textAreaStyle = "-fx-control-inner-background: white; -fx-background-color: transparent; -fx-text-fill: #606266; -fx-background-insets: 0; -fx-background-radius: 0; -fx-border-width: 0;";
        
        noticeContentText.setStyle(textAreaStyle);
        noticeContentText.setWrapText(true);
        noticeContentText.setEditable(false);
        
        helpContentText.setStyle(textAreaStyle);
        helpContentText.setWrapText(true);
        helpContentText.setEditable(false);
    }
    
    /**
     * 返回登录页面
     */
    @FXML
    public void backToLogin(ActionEvent event) {
        log.info("返回登录页面");
        Application.showView(LoginView.class);
    }
    
    /**
     * 刷新公告信息
     */
    @FXML
    public void refreshNotice() {
        if (isLoading) {
            log.info("正在加载中，请稍后再试");
            return;
        }
        
        isLoading = true;
        refreshButton.setDisable(true);
        
        // 设置加载提示
        Platform.runLater(() -> {
            noticeContentText.setText("正在加载公告信息...");
            helpContentText.setText("正在加载使用须知...");
        });
        
        // 异步加载公告信息
        CompletableFuture.supplyAsync(() -> {
            // 调用API获取公告信息
            return apiService.getNotice();
        }).thenAcceptAsync(result -> {
            Platform.runLater(() -> {
                try {
                    displayNoticeData(result);
                } finally {
                    isLoading = false;
                    refreshButton.setDisable(false);
                }
            });
        });
    }
    
    /**
     * 显示公告数据
     */
    private void displayNoticeData(CommonResult result) {
        if (result != null && result.getIsSuccess()) {
            JSONObject data = (JSONObject) result.getData();
            if (data != null) {
                String noticeContent = data.getStr("noticeContent");
                String helpContent = data.getStr("helpContent");
                
                // 显示公告信息
                if (StrUtil.isNotBlank(noticeContent)) {
                    noticeContentText.setText(noticeContent);
                } else {
                    noticeContentText.setText("暂无公告信息");
                }
                
                // 显示使用须知
                if (StrUtil.isNotBlank(helpContent)) {
                    helpContentText.setText(helpContent);
                } else {
                    helpContentText.setText("暂无使用须知");
                }
                
                log.info("公告信息加载成功");
            } else {
                setErrorMessage("返回数据为空");
            }
        } else {
            String errorMsg = result != null ? result.getMsg() : "获取失败";
            setErrorMessage(errorMsg);
        }
    }
    
    /**
     * 设置错误信息
     */
    private void setErrorMessage(String errorMsg) {
        noticeContentText.setText("加载公告信息失败: " + errorMsg);
        helpContentText.setText("加载使用须知失败: " + errorMsg);
        log.error("公告信息加载失败: {}", errorMsg);
    }
    
    /**
     * 显示提示框
     */
    private void showAlert(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(content);
            alert.showAndWait();
        });
    }
} 