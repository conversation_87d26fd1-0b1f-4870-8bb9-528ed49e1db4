# Reset Cursor Machine ID Core Logic
# ASCII characters only

# Set output encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Config file path
$STORAGE_FILE = "$env:APPDATA\Cursor\User\globalStorage\storage.json"

# Close Cursor processes
Get-Process -Name "Cursor","cursor" -ErrorAction SilentlyContinue | Stop-Process -Force

# Generate random hex string
function Get-RandomHex {
    param ([int]$length)
    $bytes = New-Object byte[] ($length)
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::new()
    $rng.GetBytes($bytes)
    $hexString = [System.BitConverter]::ToString($bytes) -replace '-',''
    $rng.Dispose()
    return $hexString
}

# Generate standard machine ID
function New-StandardMachineId {
    $template = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
    $result = $template -replace '[xy]', {
        param($match)
        $r = [Random]::new().Next(16)
        $v = if ($match.Value -eq "x") { $r } else { ($r -band 0x3) -bor 0x8 }
        return $v.ToString("x")
    }
    return $result
}

# Generate new IDs
$MAC_MACHINE_ID = New-StandardMachineId
$UUID = [System.Guid]::NewGuid().ToString()
# Generate Cursor specific machineId format
$prefixBytes = [System.Text.Encoding]::UTF8.GetBytes("auth0|user_")
$prefixHex = -join ($prefixBytes | ForEach-Object { '{0:x2}' -f $_ })
$randomPart = Get-RandomHex -length 32
$MACHINE_ID = "$prefixHex$randomPart"
$SQM_ID = "{$([System.Guid]::NewGuid().ToString().ToUpper())}"

# Update config file
try {
    # Read config file
    $config = Get-Content $STORAGE_FILE -Raw | ConvertFrom-Json
    
    # Update Cursor device IDs
    $config.'telemetry.machineId' = $MACHINE_ID
    $config.'telemetry.macMachineId' = $MAC_MACHINE_ID
    $config.'telemetry.devDeviceId' = $UUID
    $config.'telemetry.sqmId' = $SQM_ID

    # Save updated config
    $config | ConvertTo-Json -Depth 10 | Out-File -FilePath $STORAGE_FILE -Encoding UTF8
    
    Write-Host "Cursor machine ID has been reset. Please restart Cursor."
} 
catch {
    Write-Host "Failed to update config file: $_"
}

# Optional: Update Windows registry MachineGuid
$registryPath = "HKLM:\SOFTWARE\Microsoft\Cryptography"
if (Test-Path $registryPath) {
    try {
        Set-ItemProperty -Path $registryPath -Name MachineGuid -Value ([System.Guid]::NewGuid().ToString()) -Force
        Write-Host "System MachineGuid has been updated."
    } 
    catch {
        Write-Host "Failed to update MachineGuid. Admin privileges required."
    }
} 