package com.ceadeal.javafxboot;

import cn.hutool.core.util.StrUtil;
import com.ceadeal.javafxboot.ctrl.MainCtrl;
import com.ceadeal.javafxboot.service.ApiService;
import com.ceadeal.javafxboot.util.MySplashScreen;
import com.ceadeal.javafxboot.util.AppSettings;
import com.ceadeal.javafxboot.util.CursorHelper;
import com.ceadeal.javafxboot.view.LoginView;
import com.ceadeal.javafxboot.view.MainView;
import de.felixroske.jfxsupport.AbstractFxmlView;
import de.felixroske.jfxsupport.AbstractJavaFxApplicationSupport;
import javafx.application.Platform;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.File;
import java.util.Collection;
import java.util.Collections;

@Slf4j
@SpringBootApplication
public class Application extends AbstractJavaFxApplicationSupport implements ApplicationRunner {

    // 静态代码块：应用启动前设置JavaFX属性
    static {
        try {
            // 禁用JavaFX的DPI缩放
            System.setProperty("glass.win.uiScale", "100%");
            System.setProperty("prism.allowhidpi", "false");
            
            // 禁用JavaFX的字体自动缩放
            System.setProperty("prism.text", "t2k");
            System.setProperty("prism.lcdtext", "false");
            
            // 设置固定的字体大小
            System.setProperty("javafx.font.size", "14px");
            
            // 禁用触摸和手势事件（这些有时会影响缩放）
            System.setProperty("javafx.platform", "Desktop");
        } catch (Exception e) {
            System.err.println("设置JavaFX系统属性失败: " + e.getMessage());
        }
    }

    // 不使用注入，我们将在合适的时机手动获取bean
    private ApiService apiService;
    private AppSettings appSettings;

    // 保存ApplicationContext的静态引用
    private static ConfigurableApplicationContext savedCtx;

    public static void main(String[] args) {
        // 启动应用，设置初始视图为登录页面
        launch(Application.class, LoginView.class, new MySplashScreen(), args);
    }

    /**
     * Spring 容器启动时执行一些初始化操作，如：加载自定义资源...
     * 此方法自行完之后，JavaFx应用程序启动画面才会关闭，原因分析：
     * 1 de.felixroske.jfxsupport.AbstractJavaFxApplicationSupport[row:120].init() 重写了 javafx.application.Application.init()
     * 2 先启动SpringBoot应用，当SpringBoot应用启动完毕时，执行了两个异步操作，第二个异步操作是关闭启动画面
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("ApplicationRunner.run...");

        // 假装加载资源1s
        Thread.sleep(1000);
    }

    @Override
    public void init() throws Exception {
        log.info("init");
        super.init();
    }

    @Override
    public void start(Stage stage) throws Exception {
        log.info("start");
        super.start(stage);
    }

    @Override
    public void beforeShowingSplash(Stage splashStage) {
        super.beforeShowingSplash(splashStage);
        log.info("beforeShowingSplash");
    }

    @Override
    public void beforeInitialView(Stage stage, ConfigurableApplicationContext ctx) {
        log.info("beforeInitialView");
        super.beforeInitialView(stage, ctx);

        // 保存ApplicationContext以便后续使用
        savedCtx = ctx;

        // 设置窗口标题
        stage.setTitle("JavaFX应用程序");

        // 设置窗口大小
        stage.setWidth(1200);
        stage.setHeight(800);

        // 允许调整窗口大小
        stage.setResizable(true);

        // 设置窗口最小尺寸，防止内容显示不全
        stage.setMinWidth(1050);
        stage.setMinHeight(717);

        try {
            // 从Spring容器中获取必要的bean
            ApiService apiService = ctx.getBean(ApiService.class);
            AppSettings appSettings = ctx.getBean(AppSettings.class);

            // 加载用户配置
            appSettings.loadConfig();

            // 初始化Cursor数据库路径
            if (appSettings.getVscdbPath() == null || appSettings.getVscdbPath().isEmpty()) {
                String fullPath = CursorHelper.getVscdbPath();
                if (fullPath != null && !fullPath.isEmpty()) {
                    // 去掉路径中的"User/globalStorage/state.vscdb"部分
                    String basePath = fullPath;
                    int userIndex = fullPath.lastIndexOf("User");
                    if (userIndex > 0) {
                        basePath = fullPath.substring(0, userIndex);
                    }
                    appSettings.setVscdbPath(basePath);
                    appSettings.saveConfig();
                    log.info("已初始化Cursor数据库路径: {}", basePath);
                }
            }

            // 移除自动登录功能
            // 用户凭据会在LoginCtrl的initialize方法中自动填充到登录表单

        } catch (Exception e) {
            log.error("初始化应用设置出错", e);
        }
    }

    @Override
    public void stop() throws Exception {
        log.info("stop");
        super.stop();
    }

    // 虽然在application.yml中可以设置应用图标，但是首屏启动时的应用图标未改变，建议在此处覆盖默认图标
    @Override
    public Collection<Image> loadDefaultIcons() {
        return Collections.singletonList(new Image(getClass().getResource("/icon/icon.png").toExternalForm()));
    }

    /**
     * 获取Spring应用上下文
     */
    public static ConfigurableApplicationContext getApplicationContext() {
        return savedCtx;
    }
    /**
     * 安全切换视图的辅助方法
     */
    public static void safelyShowView(Class<? extends AbstractFxmlView> newView) {
        Platform.runLater(() -> {
            try {
                // 获取当前舞台
                Stage stage = getStage();
                // 先隐藏当前舞台
                if (stage.isShowing()) {
                    stage.hide();
                }

                // 等待短暂时间确保UI状态清理
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                // 然后切换视图
                showView(newView);
            } catch (Exception e) {
                log.error("视图切换失败", e);
            }
        });
    }
}