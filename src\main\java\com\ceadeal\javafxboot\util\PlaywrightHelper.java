//package com.ceadeal.javafxboot.util;
//
//import com.microsoft.playwright.*;
//import com.microsoft.playwright.options.Cookie;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.CompletableFuture;
//
///**
// * Playwright浏览器自动化工具类
// * 用于打开浏览器并管理cookie
// */
//@Slf4j
//@Component
//public class PlaywrightHelper {
//
//    private static final String CURSOR_URL = "https://www.cursor.com/cn";
//    private static final String COOKIE_NAME = "WorkosCursorSessionToken";
//
//    private Playwright playwright;
//    private Browser browser;
//    private BrowserContext context;
//    private Page mainPage; // 保存主页面引用，所有操作都在这个页面进行
//    private boolean isRunning = false;
//
//    private final AppSettings appSettings;
//
//    @Autowired
//    public PlaywrightHelper(AppSettings appSettings) {
//        this.appSettings = appSettings;
//    }
//
//    /**
//     * 打开浏览器并设置Cookie
//     * @param token 要设置的token值
//     * @return 操作是否成功
//     */
//    public boolean openBrowserAndSetCookie(String token) {
//        log.info("开始处理Cookie设置请求，当前浏览器状态: {}", isRunning ? "运行中" : "未运行");
//
//        try {
//            // 检查是否有可用的Chrome路径
//            String chromePath = appSettings.getChromePath();
//            if (chromePath == null || chromePath.isEmpty()) {
//                log.error("谷歌浏览器路径未设置");
//                return false;
//            }
//
//            // 检查浏览器状态并初始化
//            if (!prepareBrowser(chromePath)) {
//                return false;
//            }
//
//            // 设置Cookie (无论页面是否能成功加载)
//            try {
//                log.info("正在设置Cookie: {}", COOKIE_NAME);
//                context.addCookies(Arrays.asList(
//                    new Cookie(COOKIE_NAME, token)
//                        .setDomain("www.cursor.com")
//                        .setPath("/")
//                ));
//                log.info("Cookie设置成功");
//            } catch (Exception e) {
//                log.error("设置Cookie失败: {}", e.getMessage(), e);
//                return false;
//            }
//
//            // 尝试导航到Cursor网站
//            try {
//                log.info("正在导航到: {}", CURSOR_URL);
//                mainPage.navigate(CURSOR_URL, new Page.NavigateOptions().setTimeout(60000));
//                log.info("网站已成功加载");
//            } catch (Exception e) {
//                log.error("无法加载网站，但Cookie已设置: {}", e.getMessage());
//                try {
//                    mainPage.evaluate("() => { document.body.innerHTML = '<div style=\"text-align:center;margin-top:50px;font-family:Arial;\">" +
//                        "<h2>Cookie已成功设置!</h2>" +
//                        "<p>无法加载网站，但Cookie已成功设置。请手动访问: <a href=\"https://www.cursor.com\">Cursor官网</a></p>" +
//                        "</div>'; }");
//                } catch (Exception evalEx) {
//                    log.warn("无法修改页面内容: {}", evalEx.getMessage());
//                }
//            }
//
//            return true;
//        } catch (Exception e) {
//            log.error("处理过程中出错: {}", e.getMessage(), e);
//            return false;
//        }
//    }
//
//    /**
//     * 检查浏览器状态并进行初始化
//     */
//    private boolean prepareBrowser(String chromePath) {
//        // 如果浏览器已断开连接，重置状态
//        if (isRunning && (browser == null || !browser.isConnected())) {
//            log.warn("浏览器已断开连接，重置状态");
//            closeBrowser();
//        }
//
//        // 初始化Playwright（如果需要）
//        if (playwright == null) {
//            try {
//                playwright = Playwright.create();
//                log.info("Playwright创建成功");
//            } catch (Exception e) {
//                log.error("Playwright创建失败: {}", e.getMessage(), e);
//                return false;
//            }
//        }
//
//        // 如果浏览器未初始化，启动新的浏览器实例
//        if (browser == null) {
//            try {
//                BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
//                    .setExecutablePath(Paths.get(chromePath))
//                    .setHeadless(false)
//                    .setArgs(Arrays.asList("--start-maximized"));
//
//                browser = playwright.chromium().launch(launchOptions);
//                log.info("浏览器启动成功: {}", browser.version());
//
//                // 设置浏览器关闭监听器
//                browser.onDisconnected(b -> {
//                    log.info("浏览器已断开连接");
//                    closeBrowser();
//                });
//            } catch (Exception e) {
//                log.error("浏览器启动失败: {}", e.getMessage(), e);
//                if (playwright != null) {
//                    playwright.close();
//                    playwright = null;
//                }
//                return false;
//            }
//        }
//
//        try {
//            // 尝试获取所有已打开的浏览器上下文和页面
//            List<BrowserContext> contexts = browser.contexts();
//
//            if (contexts.isEmpty()) {
//                // 如果没有上下文，创建一个新的
//                log.info("没有找到现有上下文，创建新的上下文");
//                context = browser.newContext(new Browser.NewContextOptions()
//                    .setViewportSize(null));
//                mainPage = context.newPage();
//                log.info("创建了新的上下文和页面");
//            } else {
//                // 使用第一个找到的上下文
//                log.info("找到{}个现有上下文，使用第一个", contexts.size());
//                context = contexts.get(0);
//
//                // 获取所有打开的页面
//                List<Page> pages = context.pages();
//
//                if (pages.isEmpty()) {
//                    // 如果没有页面，创建一个新的
//                    log.info("在现有上下文中没有找到页面，创建新页面");
//                    mainPage = context.newPage();
//                } else {
//                    // 使用第一个找到的页面
//                    log.info("找到{}个现有页面，使用第一个", pages.size());
//                    mainPage = pages.get(0);
//                }
//            }
//
//            isRunning = true;
//            return true;
//        } catch (Exception e) {
//            log.error("初始化或获取页面失败: {}", e.getMessage(), e);
//            closeBrowser();
//            return false;
//        }
//    }
//
//    /**
//     * 关闭浏览器并释放资源
//     */
//    public synchronized void closeBrowser() {
//        if (!isRunning) {
//            return;
//        }
//
//        log.info("关闭浏览器资源");
//        try {
//            // 不关闭页面和上下文，只设置引用为null
//            mainPage = null;
//            context = null;
//
//            if (browser != null) {
//                try {
//                    browser.close();
//                } catch (Exception e) {
//                    log.error("关闭浏览器出错: {}", e.getMessage());
//                } finally {
//                    browser = null;
//                }
//            }
//
//            if (playwright != null) {
//                try {
//                    playwright.close();
//                } catch (Exception e) {
//                    log.error("关闭Playwright出错: {}", e.getMessage());
//                } finally {
//                    playwright = null;
//                }
//            }
//        } catch (Exception e) {
//            log.error("关闭资源时出错: {}", e.getMessage());
//        } finally {
//            isRunning = false;
//        }
//    }
//
//    /**
//     * 检查浏览器是否正在运行
//     */
//    public boolean isRunning() {
//        boolean connected = browser != null && browser.isConnected();
//        return isRunning && connected;
//    }
//}