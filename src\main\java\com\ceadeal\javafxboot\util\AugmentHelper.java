package com.ceadeal.javafxboot.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Augment 辅助工具类
 * 用于修改 JavaScript 文件中的 OAuth 相关方法
 */
@Slf4j
public class AugmentHelper {

    /**
     * 使用正则表达式修改 OAuth 状态对象
     * 匹配格式：{codeVerifier:t,codeChallenge:r,state:a,creationTime:...}
     * 支持变量名可变，支持空格变化
     */
    public static boolean modifyOAuthStateAdvancedRegex(String jsFilePath, String codeVerifier, String codeChallenge, String state) {
        try {
            File jsFile = new File(jsFilePath);
            if (!jsFile.exists()) {
                log.error("JavaScript 文件不存在: {}", jsFilePath);
                return false;
            }
            //文件有可能为只读，需修改权限
            if (!jsFile.canWrite()) {
                jsFile.setWritable(true);
            }
            String regex =
                    "=\\{codeVerifier:[a-z]+,codeChallenge:[a-z]+,state:[a-z]+,creationTime:";
            // 读取整个文件内容
            List<String> lines = FileUtil.readLines(jsFile, "UTF-8");
            boolean modified = false;
            for (int i = 0; i < lines.size(); i++) {
                String content= lines.get(i);
                if (StrUtil.isNotBlank(content) && content.contains("async createOAuthState")) {
                    String newContent = content.replaceAll(regex, "={codeVerifier:'" + codeVerifier + "',codeChallenge:'" + codeChallenge + "',state:'" + state + "',creationTime:");
                    modified = true;
                    lines.set(i, newContent);
                }
            }
            if (modified) {
                FileUtil.writeLines(lines, jsFilePath, "UTF-8");
            }
            return modified;

        } catch (Exception e) {
            log.error("修改文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 创建测试文件
        String testFilePath = "C:\\Users\\<USER>\\.vscode\\extensions\\augment.vscode-augment-0.496.1\\out\\extension.js";

        try {
            // 写入测试文件

            // 执行修改
            boolean result = modifyOAuthStateAdvancedRegex(
                    testFilePath,
                "TOUUKWA5MiRuNatrMWuyLqqA4TBQ8TGK2Fw8dpO-fV0",
                "_pTohs-RxT4e0G3FkBK77DULIVlQA19HzICDZ9C47eg",
                "264cb2d1-ec63-41aa-86f6-b0a71379ef45"
            );

            System.out.println("\n=== 修改结果 ===");
            System.out.println("修改成功: " + result);


        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
