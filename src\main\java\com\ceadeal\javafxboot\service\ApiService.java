package com.ceadeal.javafxboot.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ceadeal.javafxboot.ctrl.SettingsCtrl;
import com.ceadeal.javafxboot.pojo.CommonResult;
import com.ceadeal.javafxboot.util.AppSettings;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * API服务接口
 * 封装与后端交互的HTTP请求
 */
@Service
public class ApiService {
    public static final String ZMG_VERSION = "149";
    //    public static String baseUrl = "http://46673651zt.zicp.fun/admin-api";
    public static String baseUrl = "http://119.29.20.123:8080/admin-api";
//    public static String baseUrl = "http://127.0.0.1:48080/admin-api";
    public static String registerUrl = baseUrl + "/system/auth/registercurssor";
    public static String loginUrl = baseUrl + "/system/auth/logincurssor";
    public static String loginOut = baseUrl + "/system/auth/logout";
    public static String refreshEmail = baseUrl + "/cqp/exchangecoderecord/refreshEmail";
    public static String exchangeCodeUse = baseUrl + "/cqp/exchangecoderecord/exchangeCodeUse";
    public static String userInfo = baseUrl + "/cqp/exchangecoderecord/getuserinfo";
    public static String validToken = baseUrl + "/cqp/exchangecoderecord/validToken";
    public static String updatePassword = baseUrl + "/system/user/profile/update-password";
    public static String bindDevice = baseUrl + "/system/user/bindDevice";
    public static String getNotice = baseUrl + "/cqp/exchangecoderecord/getNotice";
    public static String getPublicNotice = baseUrl + "/cqp/exchangecoderecord/getPublicNotice";
    public static String getBtnControl = baseUrl + "/cqp/exchangecoderecord/getBtnControl";
    public static String getCursorEmailRecord = baseUrl + "/cqp/cursoremailrecord/selflist";

    private String token;
    
    // 存储首页数据的Map
    private Map<String, Object> homeData = new HashMap<>();
    
    // 应用设置
    private AppSettings appSettings = new AppSettings();
    
    /**
     * 构造函数，初始化默认数据
     */
    public ApiService() {
        // 初始化一些默认值
        homeData.put("username", "");
        homeData.put("cursorExpdate", "未知");
        homeData.put("email", "");
        homeData.put("numRequests", "0");
        homeData.put("maxRequestUsage", "0");
        
        // 加载应用设置
        appSettings.loadConfig();
    }

    /**
     * 注册方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 注册结果，成功返回true，失败返回false
     */
    public CommonResult register(String username, String password) {
        JSONObject params = new JSONObject();
        params.put("username", username);
        params.put("nickname", username);
        params.put("password", password);
        return doPostApi(params, registerUrl);
    }

    /**
     * 登录方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果，成功返回true，失败返回false
     */
    public String login(String username, String password) {
        // TODO: 实现实际的登录接口调用
        // 这里是模拟实现，实际应用中需要替换为真实的API调用
        JSONObject params = new JSONObject();
        params.put("username", username);
        params.put("password", password);
        params.put("rememberMe", true);
        params.put("tenantName", "智码狗");
        params.put("deviceCode", SettingsCtrl.getCPUSerial());
        CommonResult<JSONObject> result = doPostApi(params, loginUrl);
        if (!result.getIsSuccess()) {
            return result.getMsg();
        }

        JSONObject data = result.getData();
        this.setToken(data.getStr("accessToken"));
        return null;
    }

    /**
     * 获取登录接口返回的token
     * @return 登录返回的token
     */
    public String getLoginToken() {
        return this.token;
    }

    /**
     * 验证token是否有效
     * @param token 需要验证的token
     * @return 验证结果，如果token有效则返回null或空字符串，否则返回错误信息
     */
    public String validateToken(String token) {

        if (token == null || token.isEmpty()) {
            return "Token不能为空";
        }
        
        // 设置当前token
        this.setToken(token);
        
        // 调用一个简单的接口验证token有效性，例如获取用户信息
        try {
            CommonResult<JSONObject> result = doGetApi(validToken);;
            if (result.getIsSuccess()) {
                return null; // token有效
            } else {
                return result.getMsg(); // 返回错误信息
            }
        } catch (Exception e) {
            return "Token验证出错: " + e.getMessage();
        }
    }

    private CommonResult doPostApi(JSONObject params, String url) {
        HttpRequest httpRequest = HttpUtil.createPost(url);
        httpRequest.body(params.toString());
        Long tenantId = getAppSettings().getTenantId();
        if (tenantId == null) {
            throw new RuntimeException("读取代理ID失败");
        }
        httpRequest.header("tenant-id", tenantId.toString());
        if (StrUtil.isNotBlank(getToken())) {
            httpRequest.header("authorization", "Bearer " + getToken());
        }
        return doApiExec(url, httpRequest);
    }

    private CommonResult<JSONObject> doApiExec(String url, HttpRequest httpRequest) {
        // 设置连接超时和读取超时
        httpRequest.setConnectionTimeout(15000); // 15秒连接超时
        httpRequest.setReadTimeout(20000);      // 20秒读取超时

        String body = httpRequest.execute().body();
//        System.out.println(url + " 返回：" + body);
        if (StrUtil.isBlank(body)) {
            CommonResult<JSONObject> result = new CommonResult<>();
            result.setCode(500);
            result.getMsg();
            return result;
        }
        return JSONUtil.parseObj(body).toBean(CommonResult.class);
    }

    private CommonResult<JSONObject>  doGetApi(String url) {
        HttpRequest httpRequest = HttpUtil.createGet(url);
        Long tenantId = getAppSettings().getTenantId();
        if (tenantId == null) {
            throw new RuntimeException("读取代理ID失败");
        }
        httpRequest.header("tenant-id", tenantId.toString());
        if (StrUtil.isNotBlank(getToken())) {
            httpRequest.header("authorization", "Bearer " + getToken());
        }
        return doApiExec(url, httpRequest);
    }

    /**
     * 登出方法
     *
     * @return 登出结果，成功返回true，失败返回false
     */
    public boolean logout() {
        CommonResult<JSONObject> result = doPostApi(new JSONObject(),loginOut);
        if (!result.getIsSuccess()) {
            return false;
        }
        this.token = null;
        return true;
    }

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果，成功返回true，失败返回false
     */
    public CommonResult changePassword(String oldPassword, String newPassword) {
        JSONObject params = new JSONObject();
        params.put("oldPassword", oldPassword);
        params.put("newPassword", newPassword);
        return doPostApi(params, updatePassword);
    }

    /**
     * 获取公告信息
     * @return
     */
    public CommonResult getNotice() {
        return doGetApi(getNotice);
    }

    /**
     * 获取公告信息
     * @return
     */
    public CommonResult getPublicNotice() {
        // 返回测试数据
//        CommonResult<JSONObject> result = new CommonResult<>();
//        result.setCode(0); // 成功状态码
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("title", "系统重要公告");
//        jsonObject.put("content", "尊敬的用户，\n\n感谢您使用我们的Cursor代理软件！\n\n最新版本v2.0已经发布，主要更新内容：\n1. 新增Claude 3.7 Max支持\n2. 优化了账号切换逻辑\n3. 修复了若干已知问题\n\n如有任何问题，请联系客服。\n\n谢谢！");
//
//        result.setData(jsonObject);
//        return result;

        // 正式环境取消注释下面的代码
         return doGetApi(getPublicNotice);
    }
    /**
     * 兑换激活码
     *
     * @param code 激活码
     * @return 兑换结果，成功返回true，失败返回false
     */
    public CommonResult exchangeCode(String code) {
        JSONObject params = new JSONObject();
        params.put("exchangeCode", code);
        return doPostApi(params, exchangeCodeUse);
    }
    public CommonResult bindDevice(String deviceCode) {
        return doGetApi(bindDevice + "?deviceCode=" + deviceCode);
    }

    /**
     * 获取首页数据
     * @return 首页显示的统计数据
     */
    public Map<String, Object> getHomeData() {
        return homeData;
    }
    
    /**
     * 更新首页数据
     * @param key 数据键
     * @param value 数据值
     */
    public void updateHomeData(String key, Object value) {
        homeData.put(key, value);
    }

    /**
     * 获取令牌
     *
     * @return 当前令牌
     */
    public String getToken() {
        return token;
    }

    /**
     * 设置令牌
     *
     * @param token 令牌值
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 检查用户是否已登录
     *
     * @return 是否已登录
     */
    public boolean isLoggedIn() {
        return token != null && !token.isEmpty();
    }

    public CommonResult<JSONObject>  refreshEmail(int forceFlag) {
        String ap = "";
        String cpuSerial = SettingsCtrl.getCPUSerial();
        if (StrUtil.isNotBlank(cpuSerial)) {
            ap = "&deviceCode=" + cpuSerial;
        }
        return doGetApi(refreshEmail + "?version=" + ZMG_VERSION + "&forceFlag=" + forceFlag + ap);
    }

    public CommonResult<JSONObject> getUserInfo() {
        return doGetApi(userInfo);
    }
    public CommonResult<JSONObject> getBtnControl() {
        return doGetApi(getBtnControl);
    }
    /**
     * 获取应用设置
     */
    public AppSettings getAppSettings() {
        return appSettings;
    }

    public CommonResult<JSONObject> getCursorEmailRecord(JSONObject params) {
        return doPostApi(params, getCursorEmailRecord);
    }
}